-- إنشاء الجداول المفقودة في PostgreSQL

-- جدول delivery_charge_settings
CREATE TABLE IF NOT EXISTS delivery_charge_settings (
    id VARCHAR(255) PRIMARY KEY,
    zoneID VARCHAR(255),
    vendorID VARCHAR(255),
    chargeType VARCHAR(50) DEFAULT 'fixed',
    baseCharge DECIMAL(10,2) DEFAULT 0,
    perKmCharge DECIMAL(10,2) DEFAULT 0,
    minCharge DECIMAL(10,2) DEFAULT 0,
    maxCharge DECIMAL(10,2),
    freeDeliveryAbove DECIMAL(10,2),
    maxDeliveryDistance DECIMAL(8,2) DEFAULT 10,
    timeBasedCharges JSON,
    dayBasedCharges JSON,
    urgentDeliveryCharge DECIMAL(10,2) DEFAULT 0,
    scheduledDeliveryCharge DECIMAL(10,2) DEFAULT 0,
    isEnabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    effectiveFrom DATE,
    effectiveUntil DATE,
    conditions JSON,
    description TEXT,
    metadata JSON,
    createdAt TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول dinein_settings
CREATE TABLE IF NOT EXISTS dinein_settings (
    id VARCHAR(255) PRIMARY KEY,
    vendorID VARCHAR(255),
    isEnabled BOOLEAN DEFAULT TRUE,
    requireReservation BOOLEAN DEFAULT FALSE,
    maxAdvanceBookingDays INTEGER DEFAULT 30,
    minAdvanceBookingHours INTEGER DEFAULT 1,
    maxPartySize INTEGER DEFAULT 10,
    defaultReservationDuration INTEGER DEFAULT 120,
    reservationFee DECIMAL(10,2) DEFAULT 0,
    allowWalkIns BOOLEAN DEFAULT TRUE,
    availableTimeSlots JSON,
    blockedTimeSlots JSON,
    specialDaySettings JSON,
    requireDeposit BOOLEAN DEFAULT FALSE,
    depositAmount DECIMAL(10,2) DEFAULT 0,
    depositType VARCHAR(50) DEFAULT 'fixed',
    cancellationPolicy TEXT,
    cancellationHours INTEGER DEFAULT 24,
    terms TEXT,
    metadata JSON,
    createdAt TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول driver_nearby_settings
CREATE TABLE IF NOT EXISTS driver_nearby_settings (
    id VARCHAR(255) PRIMARY KEY,
    zoneID VARCHAR(255),
    searchRadius DECIMAL(8,2) DEFAULT 5,
    maxDriversToShow INTEGER DEFAULT 10,
    assignmentTimeout INTEGER DEFAULT 60,
    maxAssignmentAttempts INTEGER DEFAULT 3,
    assignmentMethod VARCHAR(50) DEFAULT 'nearest',
    considerRating BOOLEAN DEFAULT TRUE,
    minDriverRating DECIMAL(3,2) DEFAULT 3.0,
    considerCompletedOrders BOOLEAN DEFAULT TRUE,
    minCompletedOrders INTEGER DEFAULT 5,
    allowBusyDrivers BOOLEAN DEFAULT FALSE,
    locationUpdateInterval INTEGER DEFAULT 30,
    offlineThreshold INTEGER DEFAULT 300,
    priorityZones JSON,
    blacklistedDrivers JSON,
    enableAutoAssignment BOOLEAN DEFAULT TRUE,
    enableManualAssignment BOOLEAN DEFAULT TRUE,
    workingHours JSON,
    metadata JSON,
    createdAt TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول payment_gateway_settings
CREATE TABLE IF NOT EXISTS payment_gateway_settings (
    id VARCHAR(255) PRIMARY KEY,
    gatewayName VARCHAR(100),
    displayName VARCHAR(100),
    isEnabled BOOLEAN DEFAULT FALSE,
    isTestMode BOOLEAN DEFAULT TRUE,
    publicKey TEXT,
    secretKey TEXT,
    webhookSecret TEXT,
    currency VARCHAR(10) DEFAULT 'USD',
    supportedCurrencies JSON,
    supportedCountries JSON,
    transactionFee DECIMAL(5,2) DEFAULT 0,
    fixedFee DECIMAL(10,2) DEFAULT 0,
    minAmount DECIMAL(10,2) DEFAULT 1,
    maxAmount DECIMAL(10,2),
    configuration JSON,
    webhookUrl VARCHAR(255),
    successUrl VARCHAR(255),
    cancelUrl VARCHAR(255),
    logoUrl VARCHAR(255),
    description TEXT,
    sortOrder INTEGER DEFAULT 0,
    metadata JSON,
    createdAt TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول email_templates
CREATE TABLE IF NOT EXISTS email_templates (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(100),
    slug VARCHAR(100) UNIQUE,
    subject VARCHAR(255),
    body TEXT,
    bodyText TEXT,
    type VARCHAR(50),
    language VARCHAR(10) DEFAULT 'en',
    variables JSON,
    isActive BOOLEAN DEFAULT TRUE,
    isDefault BOOLEAN DEFAULT FALSE,
    fromName VARCHAR(100),
    fromEmail VARCHAR(100),
    replyTo VARCHAR(100),
    metadata JSON,
    createdAt TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول dynamic_notifications
CREATE TABLE IF NOT EXISTS dynamic_notifications (
    id VARCHAR(255) PRIMARY KEY,
    title VARCHAR(255),
    body TEXT,
    type VARCHAR(50),
    targetAudience VARCHAR(50),
    targetUsers JSON,
    targetCriteria JSON,
    priority VARCHAR(50) DEFAULT 'normal',
    status VARCHAR(50) DEFAULT 'draft',
    scheduledAt TIMESTAMP,
    sentAt TIMESTAMP,
    totalRecipients INTEGER DEFAULT 0,
    deliveredCount INTEGER DEFAULT 0,
    openedCount INTEGER DEFAULT 0,
    clickedCount INTEGER DEFAULT 0,
    imageUrl VARCHAR(255),
    actionUrl VARCHAR(255),
    actionData JSON,
    isActive BOOLEAN DEFAULT TRUE,
    expiresAt TIMESTAMP,
    createdAt TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_delivery_charge_settings_zone ON delivery_charge_settings(zoneID);
CREATE INDEX IF NOT EXISTS idx_delivery_charge_settings_vendor ON delivery_charge_settings(vendorID);
CREATE INDEX IF NOT EXISTS idx_dinein_settings_vendor ON dinein_settings(vendorID);
CREATE INDEX IF NOT EXISTS idx_driver_nearby_settings_zone ON driver_nearby_settings(zoneID);
CREATE INDEX IF NOT EXISTS idx_payment_gateway_settings_name ON payment_gateway_settings(gatewayName);
CREATE INDEX IF NOT EXISTS idx_email_templates_type ON email_templates(type);
CREATE INDEX IF NOT EXISTS idx_dynamic_notifications_status ON dynamic_notifications(status);
