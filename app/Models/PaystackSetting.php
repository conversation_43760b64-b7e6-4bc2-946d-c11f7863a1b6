<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaystackSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'paystack_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'isLiveMode',
        'publicKey',
        'secretKey',
        'webhookUrl',
        'supportedCurrencies',
        'processingFee',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isLiveMode' => 'boolean',
        'supportedCurrencies' => 'array',
        'processingFee' => 'decimal:2',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current Paystack settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'paystack_default',
            'isEnabled' => false,
            'isLiveMode' => false,
            'publicKey' => null,
            'secretKey' => null,
            'webhookUrl' => null,
            'supportedCurrencies' => ['NGN', 'USD', 'GHS', 'ZAR'],
            'processingFee' => 1.50,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
