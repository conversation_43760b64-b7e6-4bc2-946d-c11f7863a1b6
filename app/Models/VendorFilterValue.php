<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorFilterValue extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'vendor_filter_values';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'vendorID',
        'filterID',
        'values',
        'isActive',
        'createdAt',
    ];

    protected $casts = [
        'values' => 'array',
        'isActive' => 'boolean',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the vendor
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    /**
     * Get the filter
     */
    public function filter()
    {
        return $this->belongsTo(VendorFilter::class, 'filterID');
    }

    /**
     * Scope for active values
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true);
    }

    /**
     * Scope by vendor
     */
    public function scopeByVendor($query, $vendorID)
    {
        return $query->where('vendorID', $vendorID);
    }

    /**
     * Scope by filter
     */
    public function scopeByFilter($query, $filterID)
    {
        return $query->where('filterID', $filterID);
    }

    /**
     * Check if value contains specific option
     */
    public function hasValue($value)
    {
        return in_array($value, $this->values ?? []);
    }

    /**
     * Add value to the values array
     */
    public function addValue($value)
    {
        $values = $this->values ?? [];
        if (!in_array($value, $values)) {
            $values[] = $value;
            $this->values = $values;
            $this->save();
        }
        return $this;
    }

    /**
     * Remove value from the values array
     */
    public function removeValue($value)
    {
        $values = $this->values ?? [];
        $values = array_filter($values, function($v) use ($value) {
            return $v !== $value;
        });
        $this->values = array_values($values);
        $this->save();
        return $this;
    }
}
