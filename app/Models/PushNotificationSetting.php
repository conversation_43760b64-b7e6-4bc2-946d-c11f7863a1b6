<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PushNotificationSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'push_notification_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'firebaseCloudKey',
        'apiKey',
        'databaseURL',
        'storageBucket',
        'appId',
        'authDomain',
        'projectId',
        'messageSenderId',
        'measurementId',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current push notification settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'push_notification_default',
            'isEnabled' => true,
            'firebaseCloudKey' => null,
            'apiKey' => null,
            'databaseURL' => null,
            'storageBucket' => null,
            'appId' => null,
            'authDomain' => null,
            'projectId' => null,
            'messageSenderId' => null,
            'measurementId' => null,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
