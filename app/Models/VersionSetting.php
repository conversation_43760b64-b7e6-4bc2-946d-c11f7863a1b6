<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VersionSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'version_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'appVersion',
        'minVersion',
        'platform',
        'forceUpdate',
        'updateMessage',
        'downloadUrl',
        'features',
        'isEnabled',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'forceUpdate' => 'boolean',
        'features' => 'array',
        'isEnabled' => 'boolean',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get version settings by platform
     */
    public static function getByPlatform($platform)
    {
        return static::where('platform', $platform)->first();
    }

    /**
     * Scope by platform
     */
    public function scopeByPlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
