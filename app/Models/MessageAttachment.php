<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageAttachment extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'message_attachments';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'messageID',
        'conversationID',
        'type',
        'fileName',
        'filePath',
        'fileUrl',
        'mimeType',
        'fileSize',
        'width',
        'height',
        'duration',
        'thumbnailPath',
        'thumbnailUrl',
        'latitude',
        'longitude',
        'locationName',
        'caption',
        'status',
        'metadata',
        'uploadedAt',
        'createdAt',
    ];

    protected $casts = [
        'fileSize' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'duration' => 'integer',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'metadata' => 'array',
        'uploadedAt' => 'datetime',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the message
     */
    public function message()
    {
        return $this->belongsTo(ChatMessage::class, 'messageID');
    }

    /**
     * Get the conversation
     */
    public function conversation()
    {
        return $this->belongsTo(Conversation::class, 'conversationID');
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for images
     */
    public function scopeImages($query)
    {
        return $query->where('type', 'image');
    }

    /**
     * Scope for videos
     */
    public function scopeVideos($query)
    {
        return $query->where('type', 'video');
    }

    /**
     * Scope for documents
     */
    public function scopeDocuments($query)
    {
        return $query->where('type', 'document');
    }

    /**
     * Scope for locations
     */
    public function scopeLocations($query)
    {
        return $query->where('type', 'location');
    }

    /**
     * Scope for uploaded attachments
     */
    public function scopeUploaded($query)
    {
        return $query->where('status', 'uploaded');
    }

    /**
     * Mark as uploaded
     */
    public function markAsUploaded($fileUrl = null, $thumbnailUrl = null)
    {
        $this->status = 'uploaded';
        $this->uploadedAt = now();
        
        if ($fileUrl) {
            $this->fileUrl = $fileUrl;
        }
        
        if ($thumbnailUrl) {
            $this->thumbnailUrl = $thumbnailUrl;
        }
        
        $this->save();
        return $this;
    }

    /**
     * Mark as failed
     */
    public function markAsFailed($reason = null)
    {
        $this->status = 'failed';
        
        if ($reason) {
            $metadata = $this->metadata ?? [];
            $metadata['failure_reason'] = $reason;
            $this->metadata = $metadata;
        }
        
        $this->save();
        return $this;
    }

    /**
     * Check if attachment is an image
     */
    public function isImage()
    {
        return $this->type === 'image' || strpos($this->mimeType ?? '', 'image/') === 0;
    }

    /**
     * Check if attachment is a video
     */
    public function isVideo()
    {
        return $this->type === 'video' || strpos($this->mimeType ?? '', 'video/') === 0;
    }

    /**
     * Check if attachment is audio
     */
    public function isAudio()
    {
        return $this->type === 'audio' || strpos($this->mimeType ?? '', 'audio/') === 0;
    }

    /**
     * Check if attachment is a document
     */
    public function isDocument()
    {
        return $this->type === 'document' || !in_array($this->type, ['image', 'video', 'audio', 'location']);
    }

    /**
     * Check if attachment is a location
     */
    public function isLocation()
    {
        return $this->type === 'location';
    }

    /**
     * Get file size in human readable format
     */
    public function getHumanFileSize()
    {
        if (!$this->fileSize) {
            return 'Unknown';
        }

        $bytes = $this->fileSize;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get duration in human readable format
     */
    public function getHumanDuration()
    {
        if (!$this->duration) {
            return null;
        }

        $minutes = floor($this->duration / 60);
        $seconds = $this->duration % 60;
        
        return sprintf('%02d:%02d', $minutes, $seconds);
    }
}
