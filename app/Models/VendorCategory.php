<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorCategory extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'vendor_categories';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'name',
        'description',
        'photo',
        'icon',
        'color',
        'isActive',
        'sortOrder',
        'parentID',
        'metadata',
        'commissionRate',
        'createdAt',
    ];

    protected $casts = [
        'isActive' => 'boolean',
        'sortOrder' => 'integer',
        'metadata' => 'array',
        'commissionRate' => 'decimal:2',
        'createdAt' => 'datetime',
    ];

    /**
     * Get parent category
     */
    public function parent()
    {
        return $this->belongsTo(VendorCategory::class, 'parentID');
    }

    /**
     * Get child categories
     */
    public function children()
    {
        return $this->hasMany(VendorCategory::class, 'parentID');
    }

    /**
     * Get vendors in this category
     */
    public function vendors()
    {
        return $this->hasMany(Vendor::class, 'categoryID');
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true);
    }

    /**
     * Scope for parent categories
     */
    public function scopeParents($query)
    {
        return $query->whereNull('parentID');
    }

    /**
     * Scope for child categories
     */
    public function scopeChildren($query)
    {
        return $query->whereNotNull('parentID');
    }

    /**
     * Get categories ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sortOrder')->orderBy('name');
    }

    /**
     * Get full category path
     */
    public function getFullPath()
    {
        $path = [$this->name];
        
        if ($this->parent) {
            $path = array_merge($this->parent->getFullPath(), $path);
        }
        
        return $path;
    }

    /**
     * Get full category path as string
     */
    public function getFullPathString($separator = ' > ')
    {
        return implode($separator, $this->getFullPath());
    }
}
