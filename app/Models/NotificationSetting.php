<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'notification_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'senderId',
        'serviceJson',
        'isEnabled',
        'channels',
        'templates',
        'schedules',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'channels' => 'array',
        'templates' => 'array',
        'schedules' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current notification settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'notification_default',
            'senderId' => null,
            'serviceJson' => null,
            'isEnabled' => true,
            'channels' => ['push', 'email'],
            'templates' => [],
            'schedules' => [],
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
