<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StripeSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'stripe_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'isLiveMode',
        'publishableKey',
        'secretKey',
        'webhookSecret',
        'webhookUrl',
        'supportedCurrencies',
        'processingFee',
        'fixedFee',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isLiveMode' => 'boolean',
        'supportedCurrencies' => 'array',
        'processingFee' => 'decimal:2',
        'fixedFee' => 'decimal:2',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current Stripe settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'stripe_default',
            'isEnabled' => false,
            'isLiveMode' => false,
            'publishableKey' => null,
            'secretKey' => null,
            'webhookSecret' => null,
            'webhookUrl' => null,
            'supportedCurrencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
            'processingFee' => 2.90,
            'fixedFee' => 0.30,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
