<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaypalSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'paypal_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'isSandboxEnabled',
        'clientId',
        'clientSecret',
        'webhookId',
        'webhookUrl',
        'supportedCurrencies',
        'processingFee',
        'fixedFee',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isSandboxEnabled' => 'boolean',
        'supportedCurrencies' => 'array',
        'processingFee' => 'decimal:2',
        'fixedFee' => 'decimal:2',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current PayPal settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'paypal_default',
            'isEnabled' => false,
            'isSandboxEnabled' => true,
            'clientId' => null,
            'clientSecret' => null,
            'webhookId' => null,
            'webhookUrl' => null,
            'supportedCurrencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
            'processingFee' => 2.90,
            'fixedFee' => 0.30,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
