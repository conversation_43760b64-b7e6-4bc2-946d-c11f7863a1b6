<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatMessage extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'chat_messages';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'conversationID',
        'senderID',
        'receiverID',
        'senderType',
        'receiverType',
        'message',
        'messageType',
        'attachments',
        'isRead',
        'readAt',
        'orderID',
        'createdAt',
    ];

    protected $casts = [
        'attachments' => 'array',
        'isRead' => 'boolean',
        'readAt' => 'datetime',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the conversation
     */
    public function conversation()
    {
        return $this->belongsTo(Conversation::class, 'conversationID');
    }

    /**
     * Get the sender (polymorphic relationship)
     */
    public function sender()
    {
        switch ($this->senderType) {
            case 'customer':
                return $this->belongsTo(Customer::class, 'senderID');
            case 'vendor':
                return $this->belongsTo(Vendor::class, 'senderID');
            case 'driver':
                return $this->belongsTo(Driver::class, 'senderID');
            case 'admin':
                return $this->belongsTo(User::class, 'senderID');
            default:
                return null;
        }
    }

    /**
     * Get the receiver (polymorphic relationship)
     */
    public function receiver()
    {
        switch ($this->receiverType) {
            case 'customer':
                return $this->belongsTo(Customer::class, 'receiverID');
            case 'vendor':
                return $this->belongsTo(Vendor::class, 'receiverID');
            case 'driver':
                return $this->belongsTo(Driver::class, 'receiverID');
            case 'admin':
                return $this->belongsTo(User::class, 'receiverID');
            default:
                return null;
        }
    }

    /**
     * Get related order
     */
    public function order()
    {
        return $this->belongsTo(RestaurantOrder::class, 'orderID');
    }

    /**
     * Get message attachments
     */
    public function messageAttachments()
    {
        return $this->hasMany(MessageAttachment::class, 'messageID');
    }

    /**
     * Get read status for all participants
     */
    public function readStatus()
    {
        return $this->hasMany(MessageReadStatus::class, 'messageID');
    }

    /**
     * Scope for unread messages
     */
    public function scopeUnread($query)
    {
        return $query->where('isRead', false);
    }

    /**
     * Scope by message type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('messageType', $type);
    }

    /**
     * Scope by conversation
     */
    public function scopeByConversation($query, $conversationID)
    {
        return $query->where('conversationID', $conversationID);
    }

    /**
     * Scope by sender
     */
    public function scopeBySender($query, $senderID, $senderType)
    {
        return $query->where('senderID', $senderID)->where('senderType', $senderType);
    }

    /**
     * Mark message as read
     */
    public function markAsRead($userID = null, $userType = null)
    {
        $this->isRead = true;
        $this->readAt = now();
        $this->save();

        // Update read status for specific user if provided
        if ($userID && $userType) {
            MessageReadStatus::updateOrCreate(
                [
                    'messageID' => $this->id,
                    'userID' => $userID,
                    'userType' => $userType,
                ],
                [
                    'status' => 'read',
                    'readAt' => now(),
                ]
            );
        }

        return $this;
    }

    /**
     * Check if message has attachments
     */
    public function hasAttachments()
    {
        return !empty($this->attachments) || $this->messageAttachments()->exists();
    }

    /**
     * Get message preview (for notifications)
     */
    public function getPreview($length = 100)
    {
        if ($this->messageType === 'text') {
            return substr($this->message, 0, $length);
        } elseif ($this->messageType === 'image') {
            return '📷 Image';
        } elseif ($this->messageType === 'file') {
            return '📎 File';
        } elseif ($this->messageType === 'location') {
            return '📍 Location';
        } else {
            return 'Message';
        }
    }

    /**
     * Send message and update conversation
     */
    public static function sendMessage($conversationID, $senderID, $senderType, $receiverID, $receiverType, $message, $messageType = 'text', $attachments = null, $orderID = null)
    {
        $chatMessage = static::create([
            'id' => 'msg_' . uniqid(),
            'conversationID' => $conversationID,
            'senderID' => $senderID,
            'receiverID' => $receiverID,
            'senderType' => $senderType,
            'receiverType' => $receiverType,
            'message' => $message,
            'messageType' => $messageType,
            'attachments' => $attachments,
            'orderID' => $orderID,
            'isRead' => false,
            'createdAt' => now(),
        ]);

        // Update conversation stats
        $conversation = Conversation::find($conversationID);
        if ($conversation) {
            $conversation->updateStats();
        }

        // Update participant unread counts
        ConversationParticipant::where('conversationID', $conversationID)
            ->where('userID', '!=', $senderID)
            ->increment('unreadCount');

        return $chatMessage;
    }
}
