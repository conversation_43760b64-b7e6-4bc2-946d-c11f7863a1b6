<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RestaurantSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'restaurant_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'autoApproval',
        'commissionRate',
        'minOrderAmount',
        'maxDeliveryTime',
        'workingHours',
        'categories',
        'requireVerification',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'autoApproval' => 'boolean',
        'commissionRate' => 'decimal:2',
        'minOrderAmount' => 'integer',
        'maxDeliveryTime' => 'integer',
        'workingHours' => 'array',
        'categories' => 'array',
        'requireVerification' => 'boolean',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current restaurant settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'restaurant_default',
            'autoApproval' => false,
            'commissionRate' => 10.00,
            'minOrderAmount' => 0,
            'maxDeliveryTime' => 60,
            'workingHours' => [
                'monday' => ['open' => '09:00', 'close' => '22:00'],
                'tuesday' => ['open' => '09:00', 'close' => '22:00'],
                'wednesday' => ['open' => '09:00', 'close' => '22:00'],
                'thursday' => ['open' => '09:00', 'close' => '22:00'],
                'friday' => ['open' => '09:00', 'close' => '22:00'],
                'saturday' => ['open' => '09:00', 'close' => '22:00'],
                'sunday' => ['open' => '09:00', 'close' => '22:00'],
            ],
            'categories' => [],
            'requireVerification' => true,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }
}
