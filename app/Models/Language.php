<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Language extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'languages';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'code',
        'name',
        'nativeName',
        'direction',
        'flag',
        'isActive',
        'isDefault',
        'sortOrder',
        'completionPercentage',
        'regions',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isActive' => 'boolean',
        'isDefault' => 'boolean',
        'sortOrder' => 'integer',
        'completionPercentage' => 'decimal:2',
        'regions' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Scope for active languages
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true);
    }

    /**
     * Scope for default language
     */
    public function scopeDefault($query)
    {
        return $query->where('isDefault', true);
    }

    /**
     * Scope ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sortOrder')->orderBy('name');
    }

    /**
     * Get default language
     */
    public static function getDefault()
    {
        return static::where('isDefault', true)->first() ?? static::where('code', 'en')->first();
    }

    /**
     * Get language by code
     */
    public static function getByCode($code)
    {
        return static::where('code', $code)->first();
    }

    /**
     * Set as default language
     */
    public function setAsDefault()
    {
        // Remove default from other languages
        static::where('isDefault', true)->update(['isDefault' => false]);
        
        // Set this as default
        $this->isDefault = true;
        $this->save();
        
        return $this;
    }

    /**
     * Check if language is RTL
     */
    public function isRTL()
    {
        return $this->direction === 'rtl';
    }

    /**
     * Get completion status
     */
    public function getCompletionStatus()
    {
        if ($this->completionPercentage >= 100) {
            return 'complete';
        } elseif ($this->completionPercentage >= 80) {
            return 'mostly_complete';
        } elseif ($this->completionPercentage >= 50) {
            return 'partial';
        } else {
            return 'incomplete';
        }
    }

    /**
     * Update completion percentage
     */
    public function updateCompletionPercentage($percentage)
    {
        $this->completionPercentage = max(0, min(100, $percentage));
        $this->save();
        return $this;
    }

    /**
     * Get supported regions
     */
    public function getSupportedRegions()
    {
        return $this->regions ?? [];
    }

    /**
     * Add supported region
     */
    public function addRegion($region)
    {
        $regions = $this->regions ?? [];
        if (!in_array($region, $regions)) {
            $regions[] = $region;
            $this->regions = $regions;
            $this->save();
        }
        return $this;
    }

    /**
     * Remove supported region
     */
    public function removeRegion($region)
    {
        $regions = $this->regions ?? [];
        $regions = array_filter($regions, function($r) use ($region) {
            return $r !== $region;
        });
        $this->regions = array_values($regions);
        $this->save();
        return $this;
    }

    /**
     * Get common languages
     */
    public static function getCommonLanguages()
    {
        return [
            'en' => ['name' => 'English', 'nativeName' => 'English', 'direction' => 'ltr', 'flag' => '🇺🇸'],
            'ar' => ['name' => 'Arabic', 'nativeName' => 'العربية', 'direction' => 'rtl', 'flag' => '🇸🇦'],
            'es' => ['name' => 'Spanish', 'nativeName' => 'Español', 'direction' => 'ltr', 'flag' => '🇪🇸'],
            'fr' => ['name' => 'French', 'nativeName' => 'Français', 'direction' => 'ltr', 'flag' => '🇫🇷'],
            'de' => ['name' => 'German', 'nativeName' => 'Deutsch', 'direction' => 'ltr', 'flag' => '🇩🇪'],
            'it' => ['name' => 'Italian', 'nativeName' => 'Italiano', 'direction' => 'ltr', 'flag' => '🇮🇹'],
            'pt' => ['name' => 'Portuguese', 'nativeName' => 'Português', 'direction' => 'ltr', 'flag' => '🇵🇹'],
            'ru' => ['name' => 'Russian', 'nativeName' => 'Русский', 'direction' => 'ltr', 'flag' => '🇷🇺'],
            'zh' => ['name' => 'Chinese', 'nativeName' => '中文', 'direction' => 'ltr', 'flag' => '🇨🇳'],
            'ja' => ['name' => 'Japanese', 'nativeName' => '日本語', 'direction' => 'ltr', 'flag' => '🇯🇵'],
        ];
    }
}
