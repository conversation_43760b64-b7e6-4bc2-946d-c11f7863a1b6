<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'email_templates';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'name',
        'slug',
        'subject',
        'body',
        'bodyText',
        'type',
        'language',
        'variables',
        'isActive',
        'isDefault',
        'fromName',
        'fromEmail',
        'replyTo',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'variables' => 'array',
        'isActive' => 'boolean',
        'isDefault' => 'boolean',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true);
    }

    /**
     * Scope for default templates
     */
    public function scopeDefault($query)
    {
        return $query->where('isDefault', true);
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by language
     */
    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Get template by type and language
     */
    public static function getByTypeAndLanguage($type, $language = 'en')
    {
        return static::active()
                    ->byType($type)
                    ->byLanguage($language)
                    ->first() ?? static::active()->byType($type)->byLanguage('en')->first();
    }

    /**
     * Render template with variables
     */
    public function render($variables = [])
    {
        $subject = $this->subject;
        $body = $this->body;

        foreach ($variables as $key => $value) {
            $placeholder = '{{' . $key . '}}';
            $subject = str_replace($placeholder, $value, $subject);
            $body = str_replace($placeholder, $value, $body);
        }

        return [
            'subject' => $subject,
            'body' => $body,
            'from_name' => $this->fromName,
            'from_email' => $this->fromEmail,
            'reply_to' => $this->replyTo,
        ];
    }

    /**
     * Get available variables
     */
    public function getAvailableVariables()
    {
        return $this->variables ?? [];
    }

    /**
     * Set as default for type
     */
    public function setAsDefault()
    {
        // Remove default from other templates of same type and language
        static::where('type', $this->type)
              ->where('language', $this->language)
              ->where('isDefault', true)
              ->update(['isDefault' => false]);
        
        // Set this as default
        $this->isDefault = true;
        $this->save();
        
        return $this;
    }
}
