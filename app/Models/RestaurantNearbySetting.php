<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RestaurantNearbySetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'restaurant_nearby_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'radios',
        'distanceType',
        'isEnabled',
        'maxResults',
        'filters',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'radios' => 'decimal:2',
        'isEnabled' => 'boolean',
        'maxResults' => 'integer',
        'filters' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current restaurant nearby settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'restaurant_nearby_default',
            'radios' => 10.00,
            'distanceType' => 'km',
            'isEnabled' => true,
            'maxResults' => 50,
            'filters' => [],
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
