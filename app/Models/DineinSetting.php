<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DineinSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'dinein_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'vendorID',
        'isEnabled',
        'requireReservation',
        'maxAdvanceBookingDays',
        'minAdvanceBookingHours',
        'maxPartySize',
        'defaultReservationDuration',
        'reservationFee',
        'allowWalkIns',
        'availableTimeSlots',
        'blockedTimeSlots',
        'specialDaySettings',
        'requireDeposit',
        'depositAmount',
        'depositType',
        'cancellationPolicy',
        'cancellationHours',
        'terms',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'requireReservation' => 'boolean',
        'maxAdvanceBookingDays' => 'integer',
        'minAdvanceBookingHours' => 'integer',
        'maxPartySize' => 'integer',
        'defaultReservationDuration' => 'integer',
        'reservationFee' => 'decimal:2',
        'allowWalkIns' => 'boolean',
        'availableTimeSlots' => 'array',
        'blockedTimeSlots' => 'array',
        'specialDaySettings' => 'array',
        'requireDeposit' => 'boolean',
        'depositAmount' => 'decimal:2',
        'cancellationHours' => 'integer',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the vendor
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }

    /**
     * Scope by vendor
     */
    public function scopeByVendor($query, $vendorID)
    {
        return $query->where('vendorID', $vendorID);
    }

    /**
     * Get settings for vendor
     */
    public static function getForVendor($vendorID)
    {
        return static::byVendor($vendorID)->first() ?? static::getGlobal();
    }

    /**
     * Get global settings
     */
    public static function getGlobal()
    {
        return static::whereNull('vendorID')->first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault($vendorID = null)
    {
        return static::create([
            'id' => 'dinein_' . ($vendorID ? $vendorID : 'global'),
            'vendorID' => $vendorID,
            'isEnabled' => true,
            'requireReservation' => false,
            'maxAdvanceBookingDays' => 30,
            'minAdvanceBookingHours' => 1,
            'maxPartySize' => 10,
            'defaultReservationDuration' => 120,
            'reservationFee' => 0,
            'allowWalkIns' => true,
            'requireDeposit' => false,
            'depositAmount' => 0,
            'depositType' => 'fixed',
            'cancellationHours' => 24,
            'createdAt' => now(),
        ]);
    }

    /**
     * Check if reservation is allowed for date/time
     */
    public function isReservationAllowed($dateTime, $partySize = 1)
    {
        if (!$this->isEnabled) {
            return false;
        }

        // Check party size
        if ($partySize > $this->maxPartySize) {
            return false;
        }

        // Check advance booking limits
        $now = now();
        $hoursUntilReservation = $now->diffInHours($dateTime);
        
        if ($hoursUntilReservation < $this->minAdvanceBookingHours) {
            return false;
        }

        $daysUntilReservation = $now->diffInDays($dateTime);
        if ($daysUntilReservation > $this->maxAdvanceBookingDays) {
            return false;
        }

        // Check if time slot is available
        if (!$this->isTimeSlotAvailable($dateTime)) {
            return false;
        }

        return true;
    }

    /**
     * Check if time slot is available
     */
    public function isTimeSlotAvailable($dateTime)
    {
        $time = $dateTime->format('H:i');
        $date = $dateTime->format('Y-m-d');
        $dayOfWeek = strtolower($dateTime->format('l'));

        // Check blocked time slots
        if ($this->blockedTimeSlots) {
            foreach ($this->blockedTimeSlots as $blocked) {
                if ($this->isTimeInRange($time, $blocked['start'], $blocked['end'])) {
                    // Check if it's a specific date or day of week
                    if (isset($blocked['date']) && $blocked['date'] === $date) {
                        return false;
                    }
                    if (isset($blocked['day']) && $blocked['day'] === $dayOfWeek) {
                        return false;
                    }
                }
            }
        }

        // Check available time slots
        if ($this->availableTimeSlots) {
            $isInAvailableSlot = false;
            foreach ($this->availableTimeSlots as $available) {
                if ($this->isTimeInRange($time, $available['start'], $available['end'])) {
                    // Check if it's for the right day
                    if (!isset($available['day']) || $available['day'] === $dayOfWeek) {
                        $isInAvailableSlot = true;
                        break;
                    }
                }
            }
            return $isInAvailableSlot;
        }

        return true; // No restrictions
    }

    /**
     * Check if time is in range
     */
    private function isTimeInRange($time, $start, $end)
    {
        return $time >= $start && $time <= $end;
    }

    /**
     * Calculate deposit amount
     */
    public function calculateDeposit($partySize, $orderAmount = 0)
    {
        if (!$this->requireDeposit) {
            return 0;
        }

        if ($this->depositType === 'percentage') {
            return ($orderAmount * $this->depositAmount) / 100;
        }

        return $this->depositAmount; // fixed amount
    }

    /**
     * Check if cancellation is allowed
     */
    public function isCancellationAllowed($reservationDateTime)
    {
        $hoursUntilReservation = now()->diffInHours($reservationDateTime);
        return $hoursUntilReservation >= $this->cancellationHours;
    }
}
