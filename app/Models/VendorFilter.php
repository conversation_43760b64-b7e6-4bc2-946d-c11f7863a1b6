<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorFilter extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'vendor_filters';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'name',
        'type',
        'description',
        'options',
        'isActive',
        'isRequired',
        'sortOrder',
        'category',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'options' => 'array',
        'isActive' => 'boolean',
        'isRequired' => 'boolean',
        'sortOrder' => 'integer',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get vendor filter values
     */
    public function vendorValues()
    {
        return $this->hasMany(VendorFilterValue::class, 'filterID');
    }

    /**
     * Scope for active filters
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true);
    }

    /**
     * Scope for required filters
     */
    public function scopeRequired($query)
    {
        return $query->where('isRequired', true);
    }

    /**
     * Scope by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get filters ordered by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sortOrder')->orderBy('name');
    }
}
