<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'contact_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'companyName',
        'address',
        'phone',
        'alternatePhone',
        'email',
        'supportEmail',
        'website',
        'facebookUrl',
        'twitterUrl',
        'instagramUrl',
        'linkedinUrl',
        'youtubeUrl',
        'latitude',
        'longitude',
        'businessHours',
        'supportHours',
        'timezone',
        'aboutUs',
        'privacyPolicy',
        'termsOfService',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'businessHours' => 'array',
        'supportHours' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current contact settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default contact settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'contact_settings_default',
            'companyName' => 'Foodie App',
            'timezone' => 'UTC',
            'businessHours' => [
                'monday' => ['open' => '09:00', 'close' => '18:00'],
                'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                'thursday' => ['open' => '09:00', 'close' => '18:00'],
                'friday' => ['open' => '09:00', 'close' => '18:00'],
                'saturday' => ['open' => '10:00', 'close' => '16:00'],
                'sunday' => ['open' => null, 'close' => null], // Closed
            ],
            'createdAt' => now(),
        ]);
    }

    /**
     * Get social media links
     */
    public function getSocialMediaLinks()
    {
        return [
            'facebook' => $this->facebookUrl,
            'twitter' => $this->twitterUrl,
            'instagram' => $this->instagramUrl,
            'linkedin' => $this->linkedinUrl,
            'youtube' => $this->youtubeUrl,
        ];
    }

    /**
     * Check if currently open
     */
    public function isCurrentlyOpen()
    {
        $now = now($this->timezone);
        $dayOfWeek = strtolower($now->format('l'));
        
        $hours = $this->businessHours[$dayOfWeek] ?? null;
        
        if (!$hours || !$hours['open'] || !$hours['close']) {
            return false; // Closed today
        }

        $currentTime = $now->format('H:i');
        return $currentTime >= $hours['open'] && $currentTime <= $hours['close'];
    }

    /**
     * Get business hours for today
     */
    public function getTodayHours()
    {
        $today = strtolower(now($this->timezone)->format('l'));
        return $this->businessHours[$today] ?? null;
    }

    /**
     * Get support hours for today
     */
    public function getTodaySupportHours()
    {
        $today = strtolower(now($this->timezone)->format('l'));
        return $this->supportHours[$today] ?? $this->getTodayHours();
    }

    /**
     * Update business hours for a specific day
     */
    public function updateBusinessHours($day, $openTime, $closeTime)
    {
        $hours = $this->businessHours ?? [];
        $hours[strtolower($day)] = [
            'open' => $openTime,
            'close' => $closeTime,
        ];
        $this->businessHours = $hours;
        $this->save();
        return $this;
    }

    /**
     * Get formatted address
     */
    public function getFormattedAddress()
    {
        return $this->address;
    }

    /**
     * Get contact methods
     */
    public function getContactMethods()
    {
        return [
            'phone' => $this->phone,
            'alternate_phone' => $this->alternatePhone,
            'email' => $this->email,
            'support_email' => $this->supportEmail,
        ];
    }
}
