<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LanguageSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'language_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'code',
        'name',
        'nativeName',
        'flag',
        'isEnabled',
        'isDefault',
        'isRTL',
        'translations',
        'order',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isDefault' => 'boolean',
        'isRTL' => 'boolean',
        'translations' => 'array',
        'order' => 'integer',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get enabled languages
     */
    public static function getEnabled()
    {
        return static::where('isEnabled', true)->orderBy('order')->get();
    }

    /**
     * Get default language
     */
    public static function getDefault()
    {
        return static::where('isDefault', true)->first();
    }

    /**
     * Scope for enabled languages
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }

    /**
     * Scope for default language
     */
    public function scopeDefault($query)
    {
        return $query->where('isDefault', true);
    }

    /**
     * Scope for RTL languages
     */
    public function scopeRTL($query)
    {
        return $query->where('isRTL', true);
    }
}
