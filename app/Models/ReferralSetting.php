<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReferralSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'referral_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'referralAmount',
        'rewardType',
        'minOrderAmount',
        'maxReferrals',
        'validityDays',
        'isEnabled',
        'conditions',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'referralAmount' => 'decimal:2',
        'minOrderAmount' => 'decimal:2',
        'maxReferrals' => 'integer',
        'validityDays' => 'integer',
        'isEnabled' => 'boolean',
        'conditions' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current referral settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'referral_default',
            'referralAmount' => 10.00,
            'rewardType' => 'fixed',
            'minOrderAmount' => 0.00,
            'maxReferrals' => null,
            'validityDays' => 30,
            'isEnabled' => true,
            'conditions' => [],
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
