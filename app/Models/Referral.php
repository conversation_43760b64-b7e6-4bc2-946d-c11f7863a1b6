<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Referral extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'referrals';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'referrerID',
        'referrerType',
        'referredID',
        'referredType',
        'referralCode',
        'status',
        'referrerReward',
        'referredReward',
        'rewardType',
        'completedAt',
        'expiresAt',
        'campaignID',
        'metadata',
        'notes',
        'isActive',
        'createdAt',
    ];

    protected $casts = [
        'referrerReward' => 'decimal:2',
        'referredReward' => 'decimal:2',
        'completedAt' => 'datetime',
        'expiresAt' => 'datetime',
        'metadata' => 'array',
        'isActive' => 'boolean',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the referrer (polymorphic relationship)
     */
    public function referrer()
    {
        switch ($this->referrerType) {
            case 'customer':
                return $this->belongsTo(Customer::class, 'referrerID');
            case 'vendor':
                return $this->belongsTo(Vendor::class, 'referrerID');
            case 'driver':
                return $this->belongsTo(Driver::class, 'referrerID');
            default:
                return null;
        }
    }

    /**
     * Get the referred user (polymorphic relationship)
     */
    public function referred()
    {
        switch ($this->referredType) {
            case 'customer':
                return $this->belongsTo(Customer::class, 'referredID');
            case 'vendor':
                return $this->belongsTo(Vendor::class, 'referredID');
            case 'driver':
                return $this->belongsTo(Driver::class, 'referredID');
            default:
                return null;
        }
    }

    /**
     * Scope for active referrals
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true);
    }

    /**
     * Scope for completed referrals
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending referrals
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope by referrer
     */
    public function scopeByReferrer($query, $referrerID, $referrerType)
    {
        return $query->where('referrerID', $referrerID)->where('referrerType', $referrerType);
    }

    /**
     * Generate unique referral code
     */
    public static function generateReferralCode($prefix = 'REF')
    {
        do {
            $code = $prefix . strtoupper(substr(uniqid(), -6));
        } while (static::where('referralCode', $code)->exists());

        return $code;
    }

    /**
     * Create referral
     */
    public static function createReferral($referrerID, $referrerType, $referredID, $referredType, $referrerReward = 0, $referredReward = 0)
    {
        return static::create([
            'id' => 'ref_' . uniqid(),
            'referrerID' => $referrerID,
            'referrerType' => $referrerType,
            'referredID' => $referredID,
            'referredType' => $referredType,
            'referralCode' => static::generateReferralCode(),
            'status' => 'pending',
            'referrerReward' => $referrerReward,
            'referredReward' => $referredReward,
            'rewardType' => 'wallet',
            'isActive' => true,
            'createdAt' => now(),
        ]);
    }

    /**
     * Complete referral and distribute rewards
     */
    public function complete()
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->status = 'completed';
        $this->completedAt = now();
        $this->save();

        // Distribute rewards
        if ($this->referrerReward > 0) {
            $this->distributeReward($this->referrerID, $this->referrerType, $this->referrerReward, 'referral_reward');
        }

        if ($this->referredReward > 0) {
            $this->distributeReward($this->referredID, $this->referredType, $this->referredReward, 'referral_bonus');
        }

        return true;
    }

    /**
     * Distribute reward to user
     */
    private function distributeReward($userID, $userType, $amount, $reason)
    {
        if ($this->rewardType === 'wallet') {
            $wallet = Wallet::getOrCreateForUser($userID, $userType);
            $wallet->addMoney($amount, $reason, null, "Referral reward: {$this->referralCode}");
        }
        // Add other reward types as needed (points, discounts, etc.)
    }

    /**
     * Check if referral is expired
     */
    public function isExpired()
    {
        return $this->expiresAt && now() > $this->expiresAt;
    }

    /**
     * Mark as expired
     */
    public function markAsExpired()
    {
        $this->status = 'expired';
        $this->save();
        return $this;
    }
}
