<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MercadopagoSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'mercadopago_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'isSandboxEnabled',
        'publicKey',
        'accessToken',
        'webhookUrl',
        'supportedCurrencies',
        'processingFee',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isSandboxEnabled' => 'boolean',
        'supportedCurrencies' => 'array',
        'processingFee' => 'decimal:2',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current MercadoPago settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'mercadopago_default',
            'isEnabled' => false,
            'isSandboxEnabled' => true,
            'publicKey' => null,
            'accessToken' => null,
            'webhookUrl' => null,
            'supportedCurrencies' => ['ARS', 'BRL', 'CLP', 'MXN', 'COP', 'PEN', 'UYU'],
            'processingFee' => 0.00,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
