<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WalletSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'wallet_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'minTopupAmount',
        'maxTopupAmount',
        'minWithdrawAmount',
        'maxWithdrawAmount',
        'withdrawFee',
        'withdrawFeeType',
        'autoApproveWithdraw',
        'allowedPaymentMethods',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'minTopupAmount' => 'decimal:2',
        'maxTopupAmount' => 'decimal:2',
        'minWithdrawAmount' => 'decimal:2',
        'maxWithdrawAmount' => 'decimal:2',
        'withdrawFee' => 'decimal:2',
        'autoApproveWithdraw' => 'boolean',
        'allowedPaymentMethods' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current wallet settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'wallet_default',
            'isEnabled' => true,
            'minTopupAmount' => 10.00,
            'maxTopupAmount' => 1000.00,
            'minWithdrawAmount' => 50.00,
            'maxWithdrawAmount' => 5000.00,
            'withdrawFee' => 0.00,
            'withdrawFeeType' => 'fixed',
            'autoApproveWithdraw' => false,
            'allowedPaymentMethods' => ['stripe', 'paypal'],
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
