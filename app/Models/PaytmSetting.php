<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaytmSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'paytm_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'isSandboxEnabled',
        'merchantId',
        'merchantKey',
        'website',
        'industryType',
        'channelId',
        'supportedCurrencies',
        'processingFee',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isSandboxEnabled' => 'boolean',
        'supportedCurrencies' => 'array',
        'processingFee' => 'decimal:2',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current Paytm settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'paytm_default',
            'isEnabled' => false,
            'isSandboxEnabled' => true,
            'merchantId' => null,
            'merchantKey' => null,
            'website' => 'WEBSTAGING',
            'industryType' => 'Retail',
            'channelId' => 'WEB',
            'supportedCurrencies' => ['INR'],
            'processingFee' => 2.00,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
