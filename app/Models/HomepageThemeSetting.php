<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HomepageThemeSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'homepage_theme_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'theme',
        'colors',
        'layout',
        'banners',
        'sections',
        'isEnabled',
        'customCSS',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'colors' => 'array',
        'layout' => 'array',
        'banners' => 'array',
        'sections' => 'array',
        'isEnabled' => 'boolean',
        'customCSS' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current homepage theme settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'homepage_theme_default',
            'theme' => 'theme_1',
            'colors' => [
                'primary' => '#FF6B35',
                'secondary' => '#F7931E',
                'accent' => '#FFD23F'
            ],
            'layout' => [],
            'banners' => [],
            'sections' => [],
            'isEnabled' => true,
            'customCSS' => [],
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
