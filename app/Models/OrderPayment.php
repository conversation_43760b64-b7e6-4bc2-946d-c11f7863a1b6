<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderPayment extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'order_payments';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'orderID',
        'paymentMethod',
        'amount',
        'status',
        'transactionID',
        'gatewayResponse',
        'metadata',
        'paidAt',
        'refundedAt',
        'refundAmount',
        'notes',
        'createdAt',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'metadata' => 'array',
        'paidAt' => 'datetime',
        'refundedAt' => 'datetime',
        'refundAmount' => 'decimal:2',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the order
     */
    public function order()
    {
        return $this->belongsTo(RestaurantOrder::class, 'orderID');
    }

    /**
     * Scope for completed payments
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending payments
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for failed payments
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for refunded payments
     */
    public function scopeRefunded($query)
    {
        return $query->where('status', 'refunded');
    }

    /**
     * Mark payment as completed
     */
    public function markAsCompleted($transactionID = null, $gatewayResponse = null)
    {
        $this->update([
            'status' => 'completed',
            'paidAt' => now(),
            'transactionID' => $transactionID ?? $this->transactionID,
            'gatewayResponse' => $gatewayResponse ?? $this->gatewayResponse,
        ]);

        return $this;
    }

    /**
     * Mark payment as failed
     */
    public function markAsFailed($gatewayResponse = null, $notes = null)
    {
        $this->update([
            'status' => 'failed',
            'gatewayResponse' => $gatewayResponse ?? $this->gatewayResponse,
            'notes' => $notes ?? $this->notes,
        ]);

        return $this;
    }

    /**
     * Process refund
     */
    public function processRefund($amount = null, $notes = null)
    {
        $refundAmount = $amount ?? $this->amount;
        
        $this->update([
            'status' => 'refunded',
            'refundedAt' => now(),
            'refundAmount' => $refundAmount,
            'notes' => $notes ?? $this->notes,
        ]);

        return $this;
    }

    /**
     * Check if payment is completed
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if payment is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment is failed
     */
    public function isFailed()
    {
        return $this->status === 'failed';
    }

    /**
     * Check if payment is refunded
     */
    public function isRefunded()
    {
        return $this->status === 'refunded';
    }
}
