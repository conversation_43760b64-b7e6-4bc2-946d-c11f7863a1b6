<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminCommission extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'admin_commissions';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'vendorID',
        'categoryID',
        'zoneID',
        'commissionType',
        'commissionValue',
        'fixedAmount',
        'minCommission',
        'maxCommission',
        'applicableOn',
        'isEnabled',
        'effectiveFrom',
        'effectiveUntil',
        'conditions',
        'description',
        'priority',
        'createdAt',
    ];

    protected $casts = [
        'commissionValue' => 'decimal:2',
        'fixedAmount' => 'decimal:2',
        'minCommission' => 'decimal:2',
        'maxCommission' => 'decimal:2',
        'isEnabled' => 'boolean',
        'effectiveFrom' => 'date',
        'effectiveUntil' => 'date',
        'conditions' => 'array',
        'priority' => 'integer',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the vendor
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    /**
     * Get the category
     */
    public function category()
    {
        return $this->belongsTo(VendorCategory::class, 'categoryID');
    }

    /**
     * Get the zone
     */
    public function zone()
    {
        return $this->belongsTo(Zone::class, 'zoneID');
    }

    /**
     * Scope for enabled commissions
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }

    /**
     * Scope for active commissions (within date range)
     */
    public function scopeActive($query, $date = null)
    {
        $date = $date ?? now()->toDateString();
        
        return $query->where('isEnabled', true)
                    ->where(function($q) use ($date) {
                        $q->whereNull('effectiveFrom')
                          ->orWhere('effectiveFrom', '<=', $date);
                    })
                    ->where(function($q) use ($date) {
                        $q->whereNull('effectiveUntil')
                          ->orWhere('effectiveUntil', '>=', $date);
                    });
    }

    /**
     * Scope by vendor
     */
    public function scopeByVendor($query, $vendorID)
    {
        return $query->where(function($q) use ($vendorID) {
            $q->where('vendorID', $vendorID)
              ->orWhereNull('vendorID');
        });
    }

    /**
     * Scope by category
     */
    public function scopeByCategory($query, $categoryID)
    {
        return $query->where(function($q) use ($categoryID) {
            $q->where('categoryID', $categoryID)
              ->orWhereNull('categoryID');
        });
    }

    /**
     * Scope by zone
     */
    public function scopeByZone($query, $zoneID)
    {
        return $query->where(function($q) use ($zoneID) {
            $q->where('zoneID', $zoneID)
              ->orWhereNull('zoneID');
        });
    }

    /**
     * Scope ordered by priority
     */
    public function scopeOrderedByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Calculate commission for an order
     */
    public function calculateCommission($orderTotal, $deliveryFee = 0)
    {
        $applicableAmount = $this->getApplicableAmount($orderTotal, $deliveryFee);
        
        switch ($this->commissionType) {
            case 'percentage':
                $commission = ($applicableAmount * $this->commissionValue) / 100;
                break;
                
            case 'fixed':
                $commission = $this->commissionValue;
                break;
                
            case 'hybrid':
                $percentageCommission = ($applicableAmount * $this->commissionValue) / 100;
                $commission = $percentageCommission + ($this->fixedAmount ?? 0);
                break;
                
            default:
                $commission = 0;
        }

        // Apply min/max limits
        if ($this->minCommission && $commission < $this->minCommission) {
            $commission = $this->minCommission;
        }
        
        if ($this->maxCommission && $commission > $this->maxCommission) {
            $commission = $this->maxCommission;
        }

        return round($commission, 2);
    }

    /**
     * Get applicable amount based on applicableOn setting
     */
    private function getApplicableAmount($orderTotal, $deliveryFee)
    {
        switch ($this->applicableOn) {
            case 'order_total':
                return $orderTotal;
            case 'delivery_fee':
                return $deliveryFee;
            case 'both':
                return $orderTotal + $deliveryFee;
            default:
                return $orderTotal;
        }
    }

    /**
     * Check if commission is currently active
     */
    public function isCurrentlyActive()
    {
        if (!$this->isEnabled) {
            return false;
        }

        $today = now()->toDateString();
        
        if ($this->effectiveFrom && $today < $this->effectiveFrom) {
            return false;
        }
        
        if ($this->effectiveUntil && $today > $this->effectiveUntil) {
            return false;
        }

        return true;
    }

    /**
     * Get applicable commission for vendor/category/zone
     */
    public static function getApplicableCommission($vendorID = null, $categoryID = null, $zoneID = null, $date = null)
    {
        return static::active($date)
                    ->byVendor($vendorID)
                    ->byCategory($categoryID)
                    ->byZone($zoneID)
                    ->orderedByPriority()
                    ->first();
    }

    /**
     * Create default commission
     */
    public static function createDefault($commissionValue = 10, $commissionType = 'percentage')
    {
        return static::create([
            'id' => 'ac_default_' . uniqid(),
            'commissionType' => $commissionType,
            'commissionValue' => $commissionValue,
            'applicableOn' => 'order_total',
            'isEnabled' => true,
            'description' => 'Default admin commission',
            'priority' => 0,
            'createdAt' => now(),
        ]);
    }
}
