<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageReadStatus extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'message_read_status';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'messageID',
        'conversationID',
        'userID',
        'userType',
        'status',
        'deliveredAt',
        'readAt',
        'deviceID',
        'platform',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'deliveredAt' => 'datetime',
        'readAt' => 'datetime',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the message
     */
    public function message()
    {
        return $this->belongsTo(ChatMessage::class, 'messageID');
    }

    /**
     * Get the conversation
     */
    public function conversation()
    {
        return $this->belongsTo(Conversation::class, 'conversationID');
    }

    /**
     * Get the user (polymorphic relationship)
     */
    public function user()
    {
        switch ($this->userType) {
            case 'customer':
                return $this->belongsTo(Customer::class, 'userID');
            case 'vendor':
                return $this->belongsTo(Vendor::class, 'userID');
            case 'driver':
                return $this->belongsTo(Driver::class, 'userID');
            case 'admin':
                return $this->belongsTo(User::class, 'userID');
            default:
                return null;
        }
    }

    /**
     * Scope for read messages
     */
    public function scopeRead($query)
    {
        return $query->where('status', 'read');
    }

    /**
     * Scope for delivered messages
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope for failed messages
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope by user
     */
    public function scopeByUser($query, $userID, $userType)
    {
        return $query->where('userID', $userID)->where('userType', $userType);
    }

    /**
     * Mark as delivered
     */
    public function markAsDelivered($deviceID = null, $platform = null)
    {
        $this->status = 'delivered';
        $this->deliveredAt = now();
        $this->deviceID = $deviceID;
        $this->platform = $platform;
        $this->save();

        return $this;
    }

    /**
     * Mark as read
     */
    public function markAsRead($deviceID = null, $platform = null)
    {
        $this->status = 'read';
        $this->readAt = now();
        $this->deviceID = $deviceID;
        $this->platform = $platform;
        $this->save();

        return $this;
    }

    /**
     * Mark as failed
     */
    public function markAsFailed($reason = null)
    {
        $this->status = 'failed';
        if ($reason) {
            $metadata = $this->metadata ?? [];
            $metadata['failure_reason'] = $reason;
            $this->metadata = $metadata;
        }
        $this->save();

        return $this;
    }

    /**
     * Create read status for message
     */
    public static function createForMessage($messageID, $conversationID, $userID, $userType, $status = 'sent')
    {
        return static::create([
            'id' => 'mrs_' . uniqid(),
            'messageID' => $messageID,
            'conversationID' => $conversationID,
            'userID' => $userID,
            'userType' => $userType,
            'status' => $status,
            'createdAt' => now(),
        ]);
    }
}
