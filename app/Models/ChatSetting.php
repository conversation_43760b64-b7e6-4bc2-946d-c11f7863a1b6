<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'chat_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'userID',
        'userType',
        'enableNotifications',
        'enableSounds',
        'enableVibration',
        'notificationTime',
        'businessHoursStart',
        'businessHoursEnd',
        'autoReply',
        'autoReplyMessage',
        'showOnlineStatus',
        'showReadReceipts',
        'allowFileSharing',
        'maxFileSize',
        'allowedFileTypes',
        'language',
        'theme',
        'blockedUsers',
        'preferences',
        'createdAt',
    ];

    protected $casts = [
        'enableNotifications' => 'boolean',
        'enableSounds' => 'boolean',
        'enableVibration' => 'boolean',
        'businessHoursStart' => 'datetime:H:i',
        'businessHoursEnd' => 'datetime:H:i',
        'autoReply' => 'boolean',
        'showOnlineStatus' => 'boolean',
        'showReadReceipts' => 'boolean',
        'allowFileSharing' => 'boolean',
        'maxFileSize' => 'integer',
        'allowedFileTypes' => 'array',
        'blockedUsers' => 'array',
        'preferences' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the user (polymorphic relationship)
     */
    public function user()
    {
        switch ($this->userType) {
            case 'customer':
                return $this->belongsTo(Customer::class, 'userID');
            case 'vendor':
                return $this->belongsTo(Vendor::class, 'userID');
            case 'driver':
                return $this->belongsTo(Driver::class, 'userID');
            case 'admin':
                return $this->belongsTo(User::class, 'userID');
            default:
                return null;
        }
    }

    /**
     * Scope by user
     */
    public function scopeByUser($query, $userID, $userType)
    {
        return $query->where('userID', $userID)->where('userType', $userType);
    }

    /**
     * Get settings for user
     */
    public static function getForUser($userID, $userType)
    {
        return static::where('userID', $userID)
                    ->where('userType', $userType)
                    ->first() ?? static::createDefaultForUser($userID, $userType);
    }

    /**
     * Create default settings for user
     */
    public static function createDefaultForUser($userID, $userType)
    {
        return static::create([
            'id' => 'cs_' . $userType . '_' . $userID,
            'userID' => $userID,
            'userType' => $userType,
            'enableNotifications' => true,
            'enableSounds' => true,
            'enableVibration' => true,
            'notificationTime' => 'always',
            'autoReply' => false,
            'showOnlineStatus' => true,
            'showReadReceipts' => true,
            'allowFileSharing' => true,
            'maxFileSize' => 10,
            'allowedFileTypes' => ['image', 'document', 'video'],
            'language' => 'en',
            'theme' => 'light',
            'blockedUsers' => [],
            'preferences' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Check if notifications are enabled
     */
    public function shouldReceiveNotifications()
    {
        if (!$this->enableNotifications) {
            return false;
        }

        if ($this->notificationTime === 'never') {
            return false;
        }

        if ($this->notificationTime === 'business_hours') {
            return $this->isWithinBusinessHours();
        }

        return true; // always
    }

    /**
     * Check if current time is within business hours
     */
    public function isWithinBusinessHours()
    {
        if (!$this->businessHoursStart || !$this->businessHoursEnd) {
            return true;
        }

        $now = now()->format('H:i');
        $start = $this->businessHoursStart->format('H:i');
        $end = $this->businessHoursEnd->format('H:i');

        return $now >= $start && $now <= $end;
    }

    /**
     * Check if user is blocked
     */
    public function isUserBlocked($userID)
    {
        return in_array($userID, $this->blockedUsers ?? []);
    }

    /**
     * Block user
     */
    public function blockUser($userID)
    {
        $blockedUsers = $this->blockedUsers ?? [];
        if (!in_array($userID, $blockedUsers)) {
            $blockedUsers[] = $userID;
            $this->blockedUsers = $blockedUsers;
            $this->save();
        }
        return $this;
    }

    /**
     * Unblock user
     */
    public function unblockUser($userID)
    {
        $blockedUsers = $this->blockedUsers ?? [];
        $blockedUsers = array_filter($blockedUsers, function($id) use ($userID) {
            return $id !== $userID;
        });
        $this->blockedUsers = array_values($blockedUsers);
        $this->save();
        return $this;
    }

    /**
     * Check if file type is allowed
     */
    public function isFileTypeAllowed($fileType)
    {
        return in_array($fileType, $this->allowedFileTypes ?? []);
    }

    /**
     * Check if file size is within limit
     */
    public function isFileSizeAllowed($fileSizeInBytes)
    {
        $maxSizeInBytes = $this->maxFileSize * 1024 * 1024; // Convert MB to bytes
        return $fileSizeInBytes <= $maxSizeInBytes;
    }

    /**
     * Update preference
     */
    public function updatePreference($key, $value)
    {
        $preferences = $this->preferences ?? [];
        $preferences[$key] = $value;
        $this->preferences = $preferences;
        $this->save();
        return $this;
    }

    /**
     * Get preference
     */
    public function getPreference($key, $default = null)
    {
        return ($this->preferences ?? [])[$key] ?? $default;
    }
}
