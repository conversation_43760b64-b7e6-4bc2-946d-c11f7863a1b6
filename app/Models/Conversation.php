<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Conversation extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'conversations';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'type',
        'title',
        'description',
        'orderID',
        'status',
        'priority',
        'lastMessageAt',
        'lastMessageID',
        'lastMessagePreview',
        'totalMessages',
        'unreadCount',
        'isActive',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'lastMessageAt' => 'datetime',
        'totalMessages' => 'integer',
        'unreadCount' => 'integer',
        'isActive' => 'boolean',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get conversation participants
     */
    public function participants()
    {
        return $this->hasMany(ConversationParticipant::class, 'conversationID');
    }

    /**
     * Get conversation messages
     */
    public function messages()
    {
        return $this->hasMany(ChatMessage::class, 'conversationID')->orderBy('createdAt');
    }

    /**
     * Get latest messages
     */
    public function latestMessages($limit = 50)
    {
        return $this->messages()->latest('createdAt')->limit($limit);
    }

    /**
     * Get related order
     */
    public function order()
    {
        return $this->belongsTo(RestaurantOrder::class, 'orderID');
    }

    /**
     * Get last message
     */
    public function lastMessage()
    {
        return $this->belongsTo(ChatMessage::class, 'lastMessageID');
    }

    /**
     * Scope for active conversations
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true)->where('status', 'active');
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by priority
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Get conversation for specific users
     */
    public static function findBetweenUsers($user1ID, $user1Type, $user2ID, $user2Type, $orderID = null)
    {
        $conversation = static::whereHas('participants', function ($query) use ($user1ID, $user1Type) {
            $query->where('userID', $user1ID)->where('userType', $user1Type);
        })->whereHas('participants', function ($query) use ($user2ID, $user2Type) {
            $query->where('userID', $user2ID)->where('userType', $user2Type);
        });

        if ($orderID) {
            $conversation->where('orderID', $orderID);
        }

        return $conversation->first();
    }

    /**
     * Create conversation between users
     */
    public static function createBetweenUsers($user1ID, $user1Type, $user2ID, $user2Type, $orderID = null, $type = null)
    {
        // Determine conversation type if not provided
        if (!$type) {
            $type = static::determineConversationType($user1Type, $user2Type);
        }

        $conversation = static::create([
            'id' => 'conv_' . uniqid(),
            'type' => $type,
            'orderID' => $orderID,
            'status' => 'active',
            'priority' => 'normal',
            'isActive' => true,
            'totalMessages' => 0,
            'unreadCount' => 0,
            'createdAt' => now(),
        ]);

        // Add participants
        $conversation->addParticipant($user1ID, $user1Type);
        $conversation->addParticipant($user2ID, $user2Type);

        return $conversation;
    }

    /**
     * Add participant to conversation
     */
    public function addParticipant($userID, $userType, $role = 'participant')
    {
        return ConversationParticipant::create([
            'id' => 'cp_' . uniqid(),
            'conversationID' => $this->id,
            'userID' => $userID,
            'userType' => $userType,
            'role' => $role,
            'joinedAt' => now(),
            'isActive' => true,
            'createdAt' => now(),
        ]);
    }

    /**
     * Update conversation stats
     */
    public function updateStats()
    {
        $this->totalMessages = $this->messages()->count();
        $this->unreadCount = $this->messages()->where('isRead', false)->count();
        
        $lastMessage = $this->messages()->latest('createdAt')->first();
        if ($lastMessage) {
            $this->lastMessageAt = $lastMessage->createdAt;
            $this->lastMessageID = $lastMessage->id;
            $this->lastMessagePreview = substr($lastMessage->message ?? 'Attachment', 0, 100);
        }

        $this->save();
        return $this;
    }

    /**
     * Determine conversation type based on user types
     */
    private static function determineConversationType($userType1, $userType2)
    {
        $types = [$userType1, $userType2];
        sort($types);

        if ($types === ['customer', 'vendor']) {
            return 'customer_vendor';
        } elseif ($types === ['customer', 'driver']) {
            return 'customer_driver';
        } elseif ($types === ['driver', 'vendor']) {
            return 'vendor_driver';
        } elseif (in_array('admin', $types)) {
            return 'support';
        }

        return 'general';
    }
}
