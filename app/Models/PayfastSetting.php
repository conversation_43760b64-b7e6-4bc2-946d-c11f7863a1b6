<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PayfastSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'payfast_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'isSandboxEnabled',
        'merchantId',
        'merchantKey',
        'passphrase',
        'webhookUrl',
        'supportedCurrencies',
        'processingFee',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isSandboxEnabled' => 'boolean',
        'supportedCurrencies' => 'array',
        'processingFee' => 'decimal:2',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current PayFast settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'payfast_default',
            'isEnabled' => false,
            'isSandboxEnabled' => true,
            'merchantId' => null,
            'merchantKey' => null,
            'passphrase' => null,
            'webhookUrl' => null,
            'supportedCurrencies' => ['ZAR'],
            'processingFee' => 2.90,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
