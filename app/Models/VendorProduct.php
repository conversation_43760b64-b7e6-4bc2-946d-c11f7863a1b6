<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorProduct extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'vendor_products';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'vendorID',
        'foodID',
        'vendorPrice',
        'vendorDiscountPrice',
        'isAvailable',
        'stock',
        'minOrderQuantity',
        'maxOrderQuantity',
        'vendorNotes',
        'vendorAddOns',
        'vendorVariants',
        'preparationTime',
        'isSpecial',
        'sortOrder',
        'availableFrom',
        'availableUntil',
        'createdAt',
    ];

    protected $casts = [
        'vendorPrice' => 'decimal:2',
        'vendorDiscountPrice' => 'decimal:2',
        'isAvailable' => 'boolean',
        'stock' => 'integer',
        'minOrderQuantity' => 'integer',
        'maxOrderQuantity' => 'integer',
        'vendorAddOns' => 'array',
        'vendorVariants' => 'array',
        'preparationTime' => 'integer',
        'isSpecial' => 'boolean',
        'sortOrder' => 'integer',
        'availableFrom' => 'datetime',
        'availableUntil' => 'datetime',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the vendor that owns this product
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    /**
     * Get the food item
     */
    public function food()
    {
        return $this->belongsTo(Food::class, 'foodID');
    }

    /**
     * Check if product is currently available
     */
    public function isCurrentlyAvailable()
    {
        if (!$this->isAvailable) {
            return false;
        }

        $now = now();
        
        if ($this->availableFrom && $now < $this->availableFrom) {
            return false;
        }

        if ($this->availableUntil && $now > $this->availableUntil) {
            return false;
        }

        return true;
    }

    /**
     * Check if product has sufficient stock
     */
    public function hasStock($quantity = 1)
    {
        if ($this->stock === null) {
            return true; // Unlimited stock
        }

        return $this->stock >= $quantity;
    }

    /**
     * Get effective price (vendor price or food price)
     */
    public function getEffectivePrice()
    {
        return $this->vendorPrice ?? $this->food->price;
    }

    /**
     * Get effective discount price
     */
    public function getEffectiveDiscountPrice()
    {
        return $this->vendorDiscountPrice ?? $this->food->disPrice;
    }

    /**
     * Scope for available products
     */
    public function scopeAvailable($query)
    {
        return $query->where('isAvailable', true);
    }

    /**
     * Scope for special products
     */
    public function scopeSpecial($query)
    {
        return $query->where('isSpecial', true);
    }

    /**
     * Scope for products by vendor
     */
    public function scopeByVendor($query, $vendorID)
    {
        return $query->where('vendorID', $vendorID);
    }
}
