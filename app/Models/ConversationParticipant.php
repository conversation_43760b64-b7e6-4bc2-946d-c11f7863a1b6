<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConversationParticipant extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'conversation_participants';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'conversationID',
        'userID',
        'userType',
        'role',
        'canSendMessages',
        'canReceiveNotifications',
        'joinedAt',
        'leftAt',
        'lastSeenAt',
        'lastReadAt',
        'lastReadMessageID',
        'unreadCount',
        'isMuted',
        'isActive',
        'settings',
        'createdAt',
    ];

    protected $casts = [
        'canSendMessages' => 'boolean',
        'canReceiveNotifications' => 'boolean',
        'joinedAt' => 'datetime',
        'leftAt' => 'datetime',
        'lastSeenAt' => 'datetime',
        'lastReadAt' => 'datetime',
        'unreadCount' => 'integer',
        'isMuted' => 'boolean',
        'isActive' => 'boolean',
        'settings' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the conversation
     */
    public function conversation()
    {
        return $this->belongsTo(Conversation::class, 'conversationID');
    }

    /**
     * Get the user (polymorphic relationship)
     */
    public function user()
    {
        switch ($this->userType) {
            case 'customer':
                return $this->belongsTo(Customer::class, 'userID');
            case 'vendor':
                return $this->belongsTo(Vendor::class, 'userID');
            case 'driver':
                return $this->belongsTo(Driver::class, 'userID');
            case 'admin':
                return $this->belongsTo(User::class, 'userID');
            default:
                return null;
        }
    }

    /**
     * Get last read message
     */
    public function lastReadMessage()
    {
        return $this->belongsTo(ChatMessage::class, 'lastReadMessageID');
    }

    /**
     * Scope for active participants
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true);
    }

    /**
     * Scope by user
     */
    public function scopeByUser($query, $userID, $userType)
    {
        return $query->where('userID', $userID)->where('userType', $userType);
    }

    /**
     * Scope by conversation
     */
    public function scopeByConversation($query, $conversationID)
    {
        return $query->where('conversationID', $conversationID);
    }

    /**
     * Mark messages as read up to a specific message
     */
    public function markAsRead($messageID = null)
    {
        $conversation = $this->conversation;
        
        if ($messageID) {
            $message = ChatMessage::find($messageID);
            if ($message && $message->conversationID === $this->conversationID) {
                $this->lastReadAt = now();
                $this->lastReadMessageID = $messageID;
                
                // Count unread messages after this message
                $this->unreadCount = ChatMessage::where('conversationID', $this->conversationID)
                    ->where('createdAt', '>', $message->createdAt)
                    ->where('senderID', '!=', $this->userID)
                    ->count();
            }
        } else {
            // Mark all messages as read
            $this->lastReadAt = now();
            $this->unreadCount = 0;
            
            $lastMessage = $conversation->messages()->latest('createdAt')->first();
            if ($lastMessage) {
                $this->lastReadMessageID = $lastMessage->id;
            }
        }

        $this->save();
        return $this;
    }

    /**
     * Update last seen timestamp
     */
    public function updateLastSeen()
    {
        $this->lastSeenAt = now();
        $this->save();
        return $this;
    }

    /**
     * Leave conversation
     */
    public function leave()
    {
        $this->leftAt = now();
        $this->isActive = false;
        $this->save();
        return $this;
    }

    /**
     * Rejoin conversation
     */
    public function rejoin()
    {
        $this->leftAt = null;
        $this->isActive = true;
        $this->joinedAt = now();
        $this->save();
        return $this;
    }

    /**
     * Mute/unmute notifications
     */
    public function toggleMute()
    {
        $this->isMuted = !$this->isMuted;
        $this->save();
        return $this;
    }

    /**
     * Check if user can send messages
     */
    public function canSend()
    {
        return $this->isActive && $this->canSendMessages;
    }

    /**
     * Check if user should receive notifications
     */
    public function shouldReceiveNotifications()
    {
        return $this->isActive && $this->canReceiveNotifications && !$this->isMuted;
    }
}
