<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FooterTemplateSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'footer_template_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'template',
        'links',
        'socialMedia',
        'copyright',
        'isEnabled',
        'customCSS',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'links' => 'array',
        'socialMedia' => 'array',
        'isEnabled' => 'boolean',
        'customCSS' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current footer template settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'footer_template_default',
            'template' => '<footer class="footer"><div class="container"><p>&copy; {{year}} {{company_name}}. All rights reserved.</p></div></footer>',
            'links' => [
                ['name' => 'Privacy Policy', 'url' => '/privacy'],
                ['name' => 'Terms of Service', 'url' => '/terms'],
                ['name' => 'Contact Us', 'url' => '/contact']
            ],
            'socialMedia' => [
                ['name' => 'Facebook', 'url' => '', 'icon' => 'fab fa-facebook'],
                ['name' => 'Twitter', 'url' => '', 'icon' => 'fab fa-twitter'],
                ['name' => 'Instagram', 'url' => '', 'icon' => 'fab fa-instagram']
            ],
            'copyright' => '© {{year}} Foodie App. All rights reserved.',
            'isEnabled' => true,
            'customCSS' => [],
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
