<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DocumentVerificationSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'document_verification_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isDriverVerification',
        'isRestaurantVerification',
        'requiredDocuments',
        'allowedFormats',
        'maxFileSize',
        'autoApproval',
        'reviewTime',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isDriverVerification' => 'boolean',
        'isRestaurantVerification' => 'boolean',
        'requiredDocuments' => 'array',
        'allowedFormats' => 'array',
        'maxFileSize' => 'integer',
        'autoApproval' => 'boolean',
        'reviewTime' => 'integer',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current document verification settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'document_verification_default',
            'isDriverVerification' => false,
            'isRestaurantVerification' => false,
            'requiredDocuments' => [
                'driver' => ['license', 'identity'],
                'restaurant' => ['business_license', 'food_permit']
            ],
            'allowedFormats' => ['jpg', 'jpeg', 'png', 'pdf'],
            'maxFileSize' => 5,
            'autoApproval' => false,
            'reviewTime' => 24,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for driver verification
     */
    public function scopeDriverVerificationEnabled($query)
    {
        return $query->where('isDriverVerification', true);
    }

    /**
     * Scope for restaurant verification
     */
    public function scopeRestaurantVerificationEnabled($query)
    {
        return $query->where('isRestaurantVerification', true);
    }
}
