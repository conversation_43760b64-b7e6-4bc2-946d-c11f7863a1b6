<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RazorpaySetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'razorpay_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'isLiveMode',
        'keyId',
        'keySecret',
        'webhookSecret',
        'webhookUrl',
        'supportedCurrencies',
        'processingFee',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'isLiveMode' => 'boolean',
        'supportedCurrencies' => 'array',
        'processingFee' => 'decimal:2',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current Razorpay settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'razorpay_default',
            'isEnabled' => false,
            'isLiveMode' => false,
            'keyId' => null,
            'keySecret' => null,
            'webhookSecret' => null,
            'webhookUrl' => null,
            'supportedCurrencies' => ['INR', 'USD'],
            'processingFee' => 2.00,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
