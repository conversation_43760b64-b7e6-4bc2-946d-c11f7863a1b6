<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StorySetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'story_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'maxDuration',
        'maxStories',
        'allowedTypes',
        'maxFileSize',
        'filters',
        'autoDelete',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'maxDuration' => 'integer',
        'maxStories' => 'integer',
        'allowedTypes' => 'array',
        'maxFileSize' => 'integer',
        'filters' => 'array',
        'autoDelete' => 'boolean',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current story settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'story_default',
            'isEnabled' => false,
            'maxDuration' => 24,
            'maxStories' => 10,
            'allowedTypes' => ['image', 'video'],
            'maxFileSize' => 10,
            'filters' => [],
            'autoDelete' => true,
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
