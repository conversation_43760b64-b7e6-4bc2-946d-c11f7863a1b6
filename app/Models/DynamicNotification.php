<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DynamicNotification extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'dynamic_notifications';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'title',
        'body',
        'type',
        'targetAudience',
        'targetUsers',
        'targetCriteria',
        'priority',
        'status',
        'scheduledAt',
        'sentAt',
        'totalRecipients',
        'deliveredCount',
        'openedCount',
        'clickedCount',
        'imageUrl',
        'actionUrl',
        'actionData',
        'isActive',
        'expiresAt',
        'createdAt',
    ];

    protected $casts = [
        'targetUsers' => 'array',
        'targetCriteria' => 'array',
        'scheduledAt' => 'datetime',
        'sentAt' => 'datetime',
        'totalRecipients' => 'integer',
        'deliveredCount' => 'integer',
        'openedCount' => 'integer',
        'clickedCount' => 'integer',
        'actionData' => 'array',
        'isActive' => 'boolean',
        'expiresAt' => 'datetime',
        'createdAt' => 'datetime',
    ];

    /**
     * Scope for active notifications
     */
    public function scopeActive($query)
    {
        return $query->where('isActive', true);
    }

    /**
     * Scope for sent notifications
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope for scheduled notifications
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by priority
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Mark as sent
     */
    public function markAsSent($totalRecipients = 0)
    {
        $this->status = 'sent';
        $this->sentAt = now();
        $this->totalRecipients = $totalRecipients;
        $this->save();
        
        return $this;
    }

    /**
     * Update delivery stats
     */
    public function updateDeliveryStats($delivered = 0, $opened = 0, $clicked = 0)
    {
        $this->deliveredCount += $delivered;
        $this->openedCount += $opened;
        $this->clickedCount += $clicked;
        $this->save();
        
        return $this;
    }

    /**
     * Get delivery rate
     */
    public function getDeliveryRate()
    {
        return $this->totalRecipients > 0 ? 
            round(($this->deliveredCount / $this->totalRecipients) * 100, 2) : 0;
    }

    /**
     * Get open rate
     */
    public function getOpenRate()
    {
        return $this->deliveredCount > 0 ? 
            round(($this->openedCount / $this->deliveredCount) * 100, 2) : 0;
    }

    /**
     * Get click rate
     */
    public function getClickRate()
    {
        return $this->openedCount > 0 ? 
            round(($this->clickedCount / $this->openedCount) * 100, 2) : 0;
    }

    /**
     * Check if notification is expired
     */
    public function isExpired()
    {
        return $this->expiresAt && now() > $this->expiresAt;
    }

    /**
     * Get target users based on criteria
     */
    public function getTargetUsers()
    {
        if ($this->targetUsers) {
            return $this->targetUsers;
        }

        // Build query based on target audience and criteria
        $users = collect();
        
        switch ($this->targetAudience) {
            case 'customers':
                $query = Customer::query();
                break;
            case 'vendors':
                $query = Vendor::query();
                break;
            case 'drivers':
                $query = Driver::query();
                break;
            case 'all':
                // Combine all user types
                $customers = Customer::pluck('id')->map(function($id) { return ['id' => $id, 'type' => 'customer']; });
                $vendors = Vendor::pluck('id')->map(function($id) { return ['id' => $id, 'type' => 'vendor']; });
                $drivers = Driver::pluck('id')->map(function($id) { return ['id' => $id, 'type' => 'driver']; });
                return $customers->concat($vendors)->concat($drivers)->toArray();
            default:
                return [];
        }

        // Apply criteria filters
        if ($this->targetCriteria) {
            foreach ($this->targetCriteria as $criteria => $value) {
                switch ($criteria) {
                    case 'city':
                        $query->where('city', $value);
                        break;
                    case 'zone':
                        $query->where('zoneID', $value);
                        break;
                    case 'active_since':
                        $query->where('created_at', '>=', $value);
                        break;
                    // Add more criteria as needed
                }
            }
        }

        return $query->pluck('id')->map(function($id) {
            return ['id' => $id, 'type' => $this->targetAudience];
        })->toArray();
    }
}
