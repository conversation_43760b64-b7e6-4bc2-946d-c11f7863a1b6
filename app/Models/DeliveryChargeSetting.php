<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeliveryChargeSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'delivery_charge_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'zoneID',
        'vendorID',
        'chargeType',
        'baseCharge',
        'perKmCharge',
        'minCharge',
        'maxCharge',
        'freeDeliveryAbove',
        'maxDeliveryDistance',
        'timeBasedCharges',
        'dayBasedCharges',
        'urgentDeliveryCharge',
        'scheduledDeliveryCharge',
        'isEnabled',
        'priority',
        'effectiveFrom',
        'effectiveUntil',
        'conditions',
        'description',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'baseCharge' => 'decimal:2',
        'perKmCharge' => 'decimal:2',
        'minCharge' => 'decimal:2',
        'maxCharge' => 'decimal:2',
        'freeDeliveryAbove' => 'decimal:2',
        'maxDeliveryDistance' => 'decimal:2',
        'timeBasedCharges' => 'array',
        'dayBasedCharges' => 'array',
        'urgentDeliveryCharge' => 'decimal:2',
        'scheduledDeliveryCharge' => 'decimal:2',
        'isEnabled' => 'boolean',
        'priority' => 'integer',
        'effectiveFrom' => 'date',
        'effectiveUntil' => 'date',
        'conditions' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the zone
     */
    public function zone()
    {
        return $this->belongsTo(Zone::class, 'zoneID');
    }

    /**
     * Get the vendor
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }

    /**
     * Scope for active settings (within date range)
     */
    public function scopeActive($query, $date = null)
    {
        $date = $date ?? now()->toDateString();
        
        return $query->where('isEnabled', true)
                    ->where(function($q) use ($date) {
                        $q->whereNull('effectiveFrom')
                          ->orWhere('effectiveFrom', '<=', $date);
                    })
                    ->where(function($q) use ($date) {
                        $q->whereNull('effectiveUntil')
                          ->orWhere('effectiveUntil', '>=', $date);
                    });
    }

    /**
     * Scope by zone
     */
    public function scopeByZone($query, $zoneID)
    {
        return $query->where(function($q) use ($zoneID) {
            $q->where('zoneID', $zoneID)
              ->orWhereNull('zoneID');
        });
    }

    /**
     * Scope by vendor
     */
    public function scopeByVendor($query, $vendorID)
    {
        return $query->where(function($q) use ($vendorID) {
            $q->where('vendorID', $vendorID)
              ->orWhereNull('vendorID');
        });
    }

    /**
     * Scope ordered by priority
     */
    public function scopeOrderedByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Calculate delivery charge
     */
    public function calculateCharge($distance, $orderAmount = 0, $isUrgent = false, $isScheduled = false)
    {
        // Check if free delivery applies
        if ($this->freeDeliveryAbove && $orderAmount >= $this->freeDeliveryAbove) {
            return 0;
        }

        // Check distance limit
        if ($distance > $this->maxDeliveryDistance) {
            return null; // Outside delivery area
        }

        $charge = 0;

        switch ($this->chargeType) {
            case 'fixed':
                $charge = $this->baseCharge;
                break;
                
            case 'per_km':
                $charge = $distance * $this->perKmCharge;
                break;
                
            case 'dynamic':
                $charge = $this->baseCharge + ($distance * $this->perKmCharge);
                break;
        }

        // Apply time-based charges
        $charge += $this->getTimeBasedCharge();

        // Apply day-based charges
        $charge += $this->getDayBasedCharge();

        // Add urgent delivery charge
        if ($isUrgent) {
            $charge += $this->urgentDeliveryCharge;
        }

        // Add scheduled delivery charge
        if ($isScheduled) {
            $charge += $this->scheduledDeliveryCharge;
        }

        // Apply min/max limits
        if ($this->minCharge && $charge < $this->minCharge) {
            $charge = $this->minCharge;
        }
        
        if ($this->maxCharge && $charge > $this->maxCharge) {
            $charge = $this->maxCharge;
        }

        return round($charge, 2);
    }

    /**
     * Get time-based charge for current time
     */
    private function getTimeBasedCharge()
    {
        if (!$this->timeBasedCharges) {
            return 0;
        }

        $currentHour = now()->format('H:i');
        
        foreach ($this->timeBasedCharges as $timeSlot) {
            if ($currentHour >= $timeSlot['start'] && $currentHour <= $timeSlot['end']) {
                return $timeSlot['charge'] ?? 0;
            }
        }

        return 0;
    }

    /**
     * Get day-based charge for current day
     */
    private function getDayBasedCharge()
    {
        if (!$this->dayBasedCharges) {
            return 0;
        }

        $currentDay = strtolower(now()->format('l'));
        
        return $this->dayBasedCharges[$currentDay] ?? 0;
    }

    /**
     * Get applicable delivery charge setting
     */
    public static function getApplicableSetting($zoneID = null, $vendorID = null, $date = null)
    {
        return static::active($date)
                    ->byZone($zoneID)
                    ->byVendor($vendorID)
                    ->orderedByPriority()
                    ->first();
    }
}
