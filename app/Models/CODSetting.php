<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CODSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'cod_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'isEnabled',
        'minOrderAmount',
        'maxOrderAmount',
        'extraCharge',
        'chargeType',
        'allowedZones',
        'excludedZones',
        'allowedCategories',
        'excludedCategories',
        'requireVerification',
        'maxOrdersPerDay',
        'terms',
        'instructions',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'isEnabled' => 'boolean',
        'minOrderAmount' => 'decimal:2',
        'maxOrderAmount' => 'decimal:2',
        'extraCharge' => 'decimal:2',
        'allowedZones' => 'array',
        'excludedZones' => 'array',
        'allowedCategories' => 'array',
        'excludedCategories' => 'array',
        'requireVerification' => 'boolean',
        'maxOrdersPerDay' => 'integer',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current COD settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default COD settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'cod_settings_default',
            'isEnabled' => true,
            'minOrderAmount' => 0,
            'extraCharge' => 0,
            'chargeType' => 'fixed',
            'requireVerification' => false,
            'createdAt' => now(),
        ]);
    }

    /**
     * Check if COD is available for order
     */
    public function isAvailableForOrder($orderAmount, $zoneID = null, $categoryID = null)
    {
        if (!$this->isEnabled) {
            return false;
        }

        // Check minimum order amount
        if ($orderAmount < $this->minOrderAmount) {
            return false;
        }

        // Check maximum order amount
        if ($this->maxOrderAmount && $orderAmount > $this->maxOrderAmount) {
            return false;
        }

        // Check zone restrictions
        if ($zoneID) {
            if ($this->excludedZones && in_array($zoneID, $this->excludedZones)) {
                return false;
            }
            
            if ($this->allowedZones && !in_array($zoneID, $this->allowedZones)) {
                return false;
            }
        }

        // Check category restrictions
        if ($categoryID) {
            if ($this->excludedCategories && in_array($categoryID, $this->excludedCategories)) {
                return false;
            }
            
            if ($this->allowedCategories && !in_array($categoryID, $this->allowedCategories)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Calculate COD charge
     */
    public function calculateCharge($orderAmount)
    {
        if (!$this->extraCharge) {
            return 0;
        }

        if ($this->chargeType === 'percentage') {
            return ($orderAmount * $this->extraCharge) / 100;
        }

        return $this->extraCharge; // fixed charge
    }

    /**
     * Check daily COD limit for customer
     */
    public function checkDailyLimit($customerID)
    {
        if (!$this->maxOrdersPerDay) {
            return true; // No limit
        }

        $todayOrders = RestaurantOrder::where('userID', $customerID)
            ->where('paymentMethod', 'cod')
            ->whereDate('created_at', today())
            ->count();

        return $todayOrders < $this->maxOrdersPerDay;
    }

    /**
     * Add allowed zone
     */
    public function addAllowedZone($zoneID)
    {
        $zones = $this->allowedZones ?? [];
        if (!in_array($zoneID, $zones)) {
            $zones[] = $zoneID;
            $this->allowedZones = $zones;
            $this->save();
        }
        return $this;
    }

    /**
     * Remove allowed zone
     */
    public function removeAllowedZone($zoneID)
    {
        $zones = $this->allowedZones ?? [];
        $zones = array_filter($zones, function($zone) use ($zoneID) {
            return $zone !== $zoneID;
        });
        $this->allowedZones = array_values($zones);
        $this->save();
        return $this;
    }
}
