<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailSetting extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'email_settings';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'smtpHost',
        'smtpPort',
        'smtpUsername',
        'smtpPassword',
        'encryption',
        'fromEmail',
        'fromName',
        'isEnabled',
        'templates',
        'metadata',
        'createdAt',
    ];

    protected $casts = [
        'smtpPort' => 'integer',
        'isEnabled' => 'boolean',
        'templates' => 'array',
        'metadata' => 'array',
        'createdAt' => 'datetime',
    ];

    /**
     * Get current email settings
     */
    public static function getCurrent()
    {
        return static::first() ?? static::createDefault();
    }

    /**
     * Create default settings
     */
    public static function createDefault()
    {
        return static::create([
            'id' => 'email_default',
            'smtpHost' => 'smtp.gmail.com',
            'smtpPort' => 587,
            'smtpUsername' => null,
            'smtpPassword' => null,
            'encryption' => 'tls',
            'fromEmail' => null,
            'fromName' => 'Foodie App',
            'isEnabled' => true,
            'templates' => [],
            'metadata' => [],
            'createdAt' => now(),
        ]);
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('isEnabled', true);
    }
}
