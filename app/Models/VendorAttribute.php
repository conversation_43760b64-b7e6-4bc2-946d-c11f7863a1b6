<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VendorAttribute extends Model
{
    use HasFactory;

    protected $connection = 'firebase';
    protected $table = 'vendor_attributes';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'vendorID',
        'attributeType',
        'attributeName',
        'value',
        'isAvailable',
        'description',
        'icon',
        'metadata',
        'isVerified',
        'verifiedAt',
        'verifiedBy',
        'createdAt',
    ];

    protected $casts = [
        'isAvailable' => 'boolean',
        'metadata' => 'array',
        'isVerified' => 'boolean',
        'verifiedAt' => 'datetime',
        'createdAt' => 'datetime',
    ];

    /**
     * Get the vendor that owns this attribute
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendorID');
    }

    /**
     * Get the admin who verified this attribute
     */
    public function verifier()
    {
        return $this->belongsTo(User::class, 'verifiedBy');
    }

    /**
     * Scope for available attributes
     */
    public function scopeAvailable($query)
    {
        return $query->where('isAvailable', true);
    }

    /**
     * Scope for verified attributes
     */
    public function scopeVerified($query)
    {
        return $query->where('isVerified', true);
    }

    /**
     * Scope by attribute type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('attributeType', $type);
    }

    /**
     * Scope by vendor
     */
    public function scopeByVendor($query, $vendorID)
    {
        return $query->where('vendorID', $vendorID);
    }

    /**
     * Mark as verified
     */
    public function markAsVerified($verifiedBy = null)
    {
        $this->update([
            'isVerified' => true,
            'verifiedAt' => now(),
            'verifiedBy' => $verifiedBy,
        ]);

        return $this;
    }

    /**
     * Mark as unverified
     */
    public function markAsUnverified()
    {
        $this->update([
            'isVerified' => false,
            'verifiedAt' => null,
            'verifiedBy' => null,
        ]);

        return $this;
    }

    /**
     * Get common attribute types
     */
    public static function getCommonTypes()
    {
        return [
            'wifi' => 'Wi-Fi',
            'parking' => 'Parking',
            'outdoor_seating' => 'Outdoor Seating',
            'air_conditioning' => 'Air Conditioning',
            'wheelchair_accessible' => 'Wheelchair Accessible',
            'pet_friendly' => 'Pet Friendly',
            'live_music' => 'Live Music',
            'tv_screens' => 'TV Screens',
            'private_dining' => 'Private Dining',
            'takeaway' => 'Takeaway',
            'delivery' => 'Delivery',
            'credit_cards' => 'Credit Cards Accepted',
            'cash_only' => 'Cash Only',
            'reservations' => 'Reservations',
            'kids_menu' => 'Kids Menu',
            'vegetarian_options' => 'Vegetarian Options',
            'vegan_options' => 'Vegan Options',
            'halal' => 'Halal',
            'kosher' => 'Kosher',
        ];
    }
}
