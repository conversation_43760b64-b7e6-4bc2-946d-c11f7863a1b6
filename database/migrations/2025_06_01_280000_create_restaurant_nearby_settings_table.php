<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('restaurant_nearby_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->decimal('radios', 8, 2)->default(10); // Restaurant search radius
            $table->string('distanceType')->default('km'); // km or miles
            $table->boolean('isEnabled')->default(true); // Feature enabled/disabled
            $table->integer('maxResults')->default(50); // Maximum restaurants to return
            $table->json('filters')->nullable(); // Additional filters
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('restaurant_nearby_settings');
    }
};
