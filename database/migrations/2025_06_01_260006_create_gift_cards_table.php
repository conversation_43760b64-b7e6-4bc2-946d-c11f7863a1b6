<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('gift_cards', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('code')->unique(); // Gift card code
            $table->decimal('amount', 10, 2); // Gift card amount
            $table->decimal('remainingAmount', 10, 2); // Remaining balance
            $table->string('currency')->default('USD'); // Currency
            $table->string('status')->default('active'); // active, used, expired, cancelled
            $table->string('purchasedBy')->nullable(); // User who purchased
            $table->string('purchasedByType')->default('customer'); // customer, admin
            $table->string('giftedTo')->nullable(); // User who received the gift
            $table->string('giftedToType')->default('customer'); // customer type
            $table->text('message')->nullable(); // Gift message
            $table->timestamp('purchasedAt')->nullable(); // Purchase date
            $table->timestamp('redeemedAt')->nullable(); // Redemption date
            $table->timestamp('expiresAt')->nullable(); // Expiry date
            $table->json('usageHistory')->nullable(); // Usage history
            $table->json('metadata')->nullable(); // Additional gift card data
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['code']);
            $table->index(['status']);
            $table->index(['purchasedBy', 'purchasedByType']);
            $table->index(['giftedTo', 'giftedToType']);
            $table->index(['expiresAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('gift_cards');
    }
};
