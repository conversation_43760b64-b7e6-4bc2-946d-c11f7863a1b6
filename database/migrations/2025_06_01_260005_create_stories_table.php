<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('stories', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('vendorID'); // Restaurant ID
            $table->string('title')->nullable(); // Story title
            $table->text('description')->nullable(); // Story description
            $table->string('mediaType'); // image, video
            $table->string('mediaUrl'); // Media file URL
            $table->string('thumbnailUrl')->nullable(); // Thumbnail URL
            $table->integer('duration')->default(10); // Display duration in seconds
            $table->integer('viewCount')->default(0); // Total views
            $table->integer('likeCount')->default(0); // Total likes
            $table->string('status')->default('active'); // active, expired, hidden
            $table->boolean('isPromoted')->default(false); // Promoted story
            $table->timestamp('expiresAt')->nullable(); // Story expiry
            $table->json('viewers')->nullable(); // User IDs who viewed
            $table->json('likers')->nullable(); // User IDs who liked
            $table->json('metadata')->nullable(); // Additional story data
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['vendorID']);
            $table->index(['status']);
            $table->index(['isPromoted']);
            $table->index(['expiresAt']);
            $table->index(['createdAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('stories');
    }
};
