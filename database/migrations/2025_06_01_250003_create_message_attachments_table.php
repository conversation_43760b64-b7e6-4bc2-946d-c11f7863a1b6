<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('message_attachments', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('messageID'); // Message ID
            $table->string('conversationID'); // Conversation ID
            $table->string('type'); // image, video, audio, document, location
            $table->string('fileName')->nullable(); // Original file name
            $table->string('filePath'); // File storage path
            $table->string('fileUrl')->nullable(); // Public file URL
            $table->string('mimeType')->nullable(); // File MIME type
            $table->bigInteger('fileSize')->nullable(); // File size in bytes
            $table->integer('width')->nullable(); // Image/video width
            $table->integer('height')->nullable(); // Image/video height
            $table->integer('duration')->nullable(); // Audio/video duration in seconds
            $table->string('thumbnailPath')->nullable(); // Thumbnail path for images/videos
            $table->string('thumbnailUrl')->nullable(); // Thumbnail URL
            $table->decimal('latitude', 10, 8)->nullable(); // Location latitude
            $table->decimal('longitude', 11, 8)->nullable(); // Location longitude
            $table->string('locationName')->nullable(); // Location name/address
            $table->text('caption')->nullable(); // Attachment caption
            $table->string('status')->default('uploading'); // uploading, uploaded, failed
            $table->json('metadata')->nullable(); // Additional attachment data
            $table->timestamp('uploadedAt')->nullable(); // Upload completion time
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['messageID']);
            $table->index(['conversationID']);
            $table->index(['type']);
            $table->index(['status']);
            $table->index(['uploadedAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('message_attachments');
    }
};
