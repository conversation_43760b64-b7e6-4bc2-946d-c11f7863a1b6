<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('conversations', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('type'); // customer_vendor, customer_driver, vendor_driver, support
            $table->string('title')->nullable(); // Conversation title
            $table->text('description')->nullable(); // Conversation description
            $table->string('orderID')->nullable(); // Related order if any
            $table->string('status')->default('active'); // active, archived, closed
            $table->string('priority')->default('normal'); // low, normal, high, urgent
            $table->timestamp('lastMessageAt')->nullable(); // Last message timestamp
            $table->string('lastMessageID')->nullable(); // Last message ID
            $table->text('lastMessagePreview')->nullable(); // Preview of last message
            $table->integer('totalMessages')->default(0); // Total message count
            $table->integer('unreadCount')->default(0); // Unread message count
            $table->boolean('isActive')->default(true); // Conversation status
            $table->json('metadata')->nullable(); // Additional conversation data
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['type']);
            $table->index(['orderID']);
            $table->index(['status', 'isActive']);
            $table->index(['lastMessageAt']);
            $table->index(['priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('conversations');
    }
};
