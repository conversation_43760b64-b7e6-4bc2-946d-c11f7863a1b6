<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('dynamic_notifications', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('title'); // Notification title
            $table->text('body'); // Notification body
            $table->string('type'); // promotional, announcement, alert, etc.
            $table->string('targetAudience'); // all, customers, vendors, drivers
            $table->json('targetUsers')->nullable(); // Specific user IDs
            $table->json('targetCriteria')->nullable(); // Targeting criteria
            $table->string('priority')->default('normal'); // low, normal, high, urgent
            $table->string('status')->default('draft'); // draft, scheduled, sent, cancelled
            $table->timestamp('scheduledAt')->nullable(); // When to send
            $table->timestamp('sentAt')->nullable(); // When it was sent
            $table->integer('totalRecipients')->default(0); // Total recipients
            $table->integer('deliveredCount')->default(0); // Successfully delivered
            $table->integer('openedCount')->default(0); // Opened notifications
            $table->integer('clickedCount')->default(0); // Clicked notifications
            $table->string('imageUrl')->nullable(); // Notification image
            $table->string('actionUrl')->nullable(); // Action URL when clicked
            $table->json('actionData')->nullable(); // Additional action data
            $table->boolean('isActive')->default(true); // Notification status
            $table->timestamp('expiresAt')->nullable(); // Expiry date
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['type']);
            $table->index(['targetAudience']);
            $table->index(['status']);
            $table->index(['scheduledAt']);
            $table->index(['priority']);
            $table->index(['expiresAt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('dynamic_notifications');
    }
};
