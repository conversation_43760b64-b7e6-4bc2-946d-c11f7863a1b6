<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('conversation_participants', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('conversationID'); // Conversation ID
            $table->string('userID'); // Participant user ID
            $table->string('userType'); // customer, vendor, driver, admin
            $table->string('role')->default('participant'); // participant, moderator, admin
            $table->boolean('canSendMessages')->default(true); // Can send messages
            $table->boolean('canReceiveNotifications')->default(true); // Receive notifications
            $table->timestamp('joinedAt')->nullable(); // When user joined
            $table->timestamp('leftAt')->nullable(); // When user left (if applicable)
            $table->timestamp('lastSeenAt')->nullable(); // Last seen timestamp
            $table->timestamp('lastReadAt')->nullable(); // Last read timestamp
            $table->string('lastReadMessageID')->nullable(); // Last read message ID
            $table->integer('unreadCount')->default(0); // Unread messages for this user
            $table->boolean('isMuted')->default(false); // Muted notifications
            $table->boolean('isActive')->default(true); // Active participant
            $table->json('settings')->nullable(); // Participant specific settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['conversationID']);
            $table->index(['userID', 'userType']);
            $table->index(['conversationID', 'userID']);
            $table->index(['conversationID', 'isActive']);
            $table->index(['lastSeenAt']);
            $table->unique(['conversationID', 'userID', 'userType']); // One participation per user per conversation
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('conversation_participants');
    }
};
