<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('razorpay_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(false); // Razorpay enabled/disabled
            $table->boolean('isLiveMode')->default(false); // Live mode
            $table->string('keyId')->nullable(); // Razorpay key ID
            $table->string('keySecret')->nullable(); // Razorpay key secret
            $table->string('webhookSecret')->nullable(); // Webhook secret
            $table->string('webhookUrl')->nullable(); // Webhook URL
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->decimal('processingFee', 5, 2)->default(2.0); // Processing fee percentage
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('razorpay_settings');
    }
};
