<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('vendor_attributes', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('vendorID'); // Restaurant ID
            $table->string('attributeType'); // wifi, parking, outdoor_seating, etc.
            $table->string('attributeName'); // Human readable name
            $table->string('value')->nullable(); // Attribute value (yes/no, speed, capacity, etc.)
            $table->boolean('isAvailable')->default(true); // Attribute availability
            $table->text('description')->nullable(); // Additional details
            $table->string('icon')->nullable(); // Attribute icon
            $table->json('metadata')->nullable(); // Additional attribute data
            $table->boolean('isVerified')->default(false); // Verified by admin
            $table->timestamp('verifiedAt')->nullable(); // Verification timestamp
            $table->string('verifiedBy')->nullable(); // Admin who verified
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['vendorID']);
            $table->index(['attributeType']);
            $table->index(['vendorID', 'attributeType']);
            $table->index(['vendorID', 'isAvailable']);
            $table->unique(['vendorID', 'attributeType']); // One attribute per type per vendor
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('vendor_attributes');
    }
};
