<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('vendor_filter_values', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('vendorID'); // Restaurant ID
            $table->string('filterID'); // Filter ID from vendor_filters
            $table->json('values'); // Selected filter values
            $table->boolean('isActive')->default(true); // Status
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['vendorID']);
            $table->index(['filterID']);
            $table->index(['vendorID', 'filterID']);
            $table->unique(['vendorID', 'filterID']); // One value per filter per vendor
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('vendor_filter_values');
    }
};
