<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('language_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('code')->unique(); // Language code (en, ar, fr, etc.)
            $table->string('name'); // Language name
            $table->string('nativeName')->nullable(); // Native language name
            $table->string('flag')->nullable(); // Flag image URL
            $table->boolean('isEnabled')->default(true); // Language enabled/disabled
            $table->boolean('isDefault')->default(false); // Default language
            $table->boolean('isRTL')->default(false); // Right-to-left language
            $table->json('translations')->nullable(); // Translation data
            $table->integer('order')->default(0); // Display order
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled', 'isDefault']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('language_settings');
    }
};
