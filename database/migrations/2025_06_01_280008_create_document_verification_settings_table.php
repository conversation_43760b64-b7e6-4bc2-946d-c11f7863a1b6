<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('document_verification_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isDriverVerification')->default(false); // Driver verification enabled
            $table->boolean('isRestaurantVerification')->default(false); // Restaurant verification enabled
            $table->json('requiredDocuments')->nullable(); // Required document types
            $table->json('allowedFormats')->nullable(); // Allowed file formats
            $table->integer('maxFileSize')->default(5); // Max file size in MB
            $table->boolean('autoApproval')->default(false); // Auto approve documents
            $table->integer('reviewTime')->default(24); // Review time in hours
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isDriverVerification', 'isRestaurantVerification']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('document_verification_settings');
    }
};
