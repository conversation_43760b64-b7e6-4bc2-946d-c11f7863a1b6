<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('paypal_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(false); // PayPal enabled/disabled
            $table->boolean('isSandboxEnabled')->default(true); // Sandbox mode
            $table->string('clientId')->nullable(); // PayPal client ID
            $table->string('clientSecret')->nullable(); // PayPal client secret
            $table->string('webhookId')->nullable(); // Webhook ID
            $table->string('webhookUrl')->nullable(); // Webhook URL
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->decimal('processingFee', 5, 2)->default(2.9); // Processing fee percentage
            $table->decimal('fixedFee', 10, 2)->default(0.30); // Fixed fee amount
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('paypal_settings');
    }
};
