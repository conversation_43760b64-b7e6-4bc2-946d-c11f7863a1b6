<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('payfast_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(false); // PayFast enabled/disabled
            $table->boolean('isSandboxEnabled')->default(true); // Sandbox mode
            $table->string('merchantId')->nullable(); // PayFast merchant ID
            $table->string('merchantKey')->nullable(); // PayFast merchant key
            $table->string('passphrase')->nullable(); // PayFast passphrase
            $table->string('webhookUrl')->nullable(); // Webhook URL
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->decimal('processingFee', 5, 2)->default(2.9); // Processing fee percentage
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('payfast_settings');
    }
};
