<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('wallet', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('userID'); // Customer, Vendor, or Driver ID
            $table->string('userType'); // customer, vendor, driver
            $table->decimal('balance', 15, 2)->default(0); // Current wallet balance
            $table->decimal('totalEarned', 15, 2)->default(0); // Total amount earned
            $table->decimal('totalSpent', 15, 2)->default(0); // Total amount spent
            $table->decimal('totalWithdrawn', 15, 2)->default(0); // Total amount withdrawn
            $table->boolean('isActive')->default(true); // Wallet status
            $table->boolean('isBlocked')->default(false); // Blocked status
            $table->string('currency')->default('USD'); // Wallet currency
            $table->timestamp('lastTransactionAt')->nullable(); // Last transaction time
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->unique(['userID', 'userType']); // One wallet per user per type
            $table->index(['userID', 'userType']);
            $table->index(['isActive', 'isBlocked']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('wallet');
    }
};
