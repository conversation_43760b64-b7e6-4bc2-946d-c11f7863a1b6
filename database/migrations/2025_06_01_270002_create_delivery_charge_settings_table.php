<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('delivery_charge_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('zoneID')->nullable(); // Specific zone (null for global)
            $table->string('vendorID')->nullable(); // Specific vendor (null for global)
            $table->string('chargeType')->default('fixed'); // fixed, per_km, dynamic
            $table->decimal('baseCharge', 10, 2)->default(0); // Base delivery charge
            $table->decimal('perKmCharge', 10, 2)->default(0); // Charge per kilometer
            $table->decimal('minCharge', 10, 2)->default(0); // Minimum delivery charge
            $table->decimal('maxCharge', 10, 2)->nullable(); // Maximum delivery charge
            $table->decimal('freeDeliveryAbove', 10, 2)->nullable(); // Free delivery threshold
            $table->decimal('maxDeliveryDistance', 8, 2)->default(10); // Max delivery distance in km
            $table->json('timeBasedCharges')->nullable(); // Different charges for different times
            $table->json('dayBasedCharges')->nullable(); // Different charges for different days
            $table->decimal('urgentDeliveryCharge', 10, 2)->default(0); // Extra charge for urgent delivery
            $table->decimal('scheduledDeliveryCharge', 10, 2)->default(0); // Extra charge for scheduled delivery
            $table->boolean('isEnabled')->default(true); // Setting enabled/disabled
            $table->integer('priority')->default(0); // Priority for multiple rules
            $table->date('effectiveFrom')->nullable(); // Effective start date
            $table->date('effectiveUntil')->nullable(); // Effective end date
            $table->json('conditions')->nullable(); // Additional conditions
            $table->text('description')->nullable(); // Setting description
            $table->json('metadata')->nullable(); // Additional delivery charge data
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['zoneID']);
            $table->index(['vendorID']);
            $table->index(['isEnabled']);
            $table->index(['priority']);
            $table->index(['effectiveFrom', 'effectiveUntil']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('delivery_charge_settings');
    }
};
