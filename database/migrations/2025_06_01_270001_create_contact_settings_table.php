<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('contact_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('companyName')->nullable(); // Company name
            $table->text('address')->nullable(); // Company address
            $table->string('phone')->nullable(); // Primary phone number
            $table->string('alternatePhone')->nullable(); // Secondary phone number
            $table->string('email')->nullable(); // Primary email
            $table->string('supportEmail')->nullable(); // Support email
            $table->string('website')->nullable(); // Company website
            $table->string('facebookUrl')->nullable(); // Facebook page URL
            $table->string('twitterUrl')->nullable(); // Twitter profile URL
            $table->string('instagramUrl')->nullable(); // Instagram profile URL
            $table->string('linkedinUrl')->nullable(); // LinkedIn profile URL
            $table->string('youtubeUrl')->nullable(); // YouTube channel URL
            $table->decimal('latitude', 10, 8)->nullable(); // Office latitude
            $table->decimal('longitude', 11, 8)->nullable(); // Office longitude
            $table->json('businessHours')->nullable(); // Business hours for each day
            $table->json('supportHours')->nullable(); // Support hours
            $table->string('timezone')->default('UTC'); // Company timezone
            $table->text('aboutUs')->nullable(); // About us description
            $table->text('privacyPolicy')->nullable(); // Privacy policy
            $table->text('termsOfService')->nullable(); // Terms of service
            $table->json('metadata')->nullable(); // Additional contact data
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('contact_settings');
    }
};
