<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('mercadopago_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(false); // MercadoPago enabled/disabled
            $table->boolean('isSandboxEnabled')->default(true); // Sandbox mode
            $table->string('publicKey')->nullable(); // MercadoPago public key
            $table->string('accessToken')->nullable(); // MercadoPago access token
            $table->string('webhookUrl')->nullable(); // Webhook URL
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->decimal('processingFee', 5, 2)->default(0); // Processing fee percentage
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('mercadopago_settings');
    }
};
