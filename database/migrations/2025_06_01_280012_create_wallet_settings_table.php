<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('wallet_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(true); // Wallet enabled/disabled
            $table->decimal('minTopupAmount', 10, 2)->default(10); // Minimum topup amount
            $table->decimal('maxTopupAmount', 10, 2)->default(1000); // Maximum topup amount
            $table->decimal('minWithdrawAmount', 10, 2)->default(50); // Minimum withdraw amount
            $table->decimal('maxWithdrawAmount', 10, 2)->default(5000); // Maximum withdraw amount
            $table->decimal('withdrawFee', 10, 2)->default(0); // Withdrawal fee
            $table->string('withdrawFeeType')->default('fixed'); // fixed, percentage
            $table->boolean('autoApproveWithdraw')->default(false); // Auto approve withdrawals
            $table->json('allowedPaymentMethods')->nullable(); // Allowed payment methods for topup
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('wallet_settings');
    }
};
