<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('languages', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('code')->unique(); // Language code (en, ar, es, etc.)
            $table->string('name'); // Language name in English
            $table->string('nativeName'); // Language name in native script
            $table->string('direction')->default('ltr'); // ltr, rtl
            $table->string('flag')->nullable(); // Flag icon or emoji
            $table->boolean('isActive')->default(true); // Language status
            $table->boolean('isDefault')->default(false); // Default language
            $table->integer('sortOrder')->default(0); // Display order
            $table->decimal('completionPercentage', 5, 2)->default(0); // Translation completion %
            $table->json('regions')->nullable(); // Supported regions/countries
            $table->json('metadata')->nullable(); // Additional language data
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isActive']);
            $table->index(['isDefault']);
            $table->index(['sortOrder']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('languages');
    }
};
