<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('admin_commissions', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('vendorID')->nullable(); // Specific vendor (null for global)
            $table->string('categoryID')->nullable(); // Specific category (null for global)
            $table->string('zoneID')->nullable(); // Specific zone (null for global)
            $table->string('commissionType'); // percentage, fixed, hybrid
            $table->decimal('commissionValue', 10, 2); // Commission value
            $table->decimal('fixedAmount', 10, 2)->nullable(); // Fixed amount for hybrid
            $table->decimal('minCommission', 10, 2)->nullable(); // Minimum commission
            $table->decimal('maxCommission', 10, 2)->nullable(); // Maximum commission
            $table->string('applicableOn')->default('order_total'); // order_total, delivery_fee, both
            $table->boolean('isEnabled')->default(true); // Commission status
            $table->date('effectiveFrom')->nullable(); // Effective start date
            $table->date('effectiveUntil')->nullable(); // Effective end date
            $table->json('conditions')->nullable(); // Additional conditions
            $table->text('description')->nullable(); // Commission description
            $table->integer('priority')->default(0); // Priority for multiple rules
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['vendorID']);
            $table->index(['categoryID']);
            $table->index(['zoneID']);
            $table->index(['isEnabled']);
            $table->index(['effectiveFrom', 'effectiveUntil']);
            $table->index(['priority']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('admin_commissions');
    }
};
