<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('cod_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(true); // COD enabled/disabled
            $table->decimal('minOrderAmount', 10, 2)->default(0); // Minimum order for COD
            $table->decimal('maxOrderAmount', 10, 2)->nullable(); // Maximum order for COD
            $table->decimal('extraCharge', 10, 2)->default(0); // Extra charge for COD
            $table->string('chargeType')->default('fixed'); // fixed, percentage
            $table->json('allowedZones')->nullable(); // Zones where COD is allowed
            $table->json('excludedZones')->nullable(); // Zones where COD is not allowed
            $table->json('allowedCategories')->nullable(); // Categories that support COD
            $table->json('excludedCategories')->nullable(); // Categories that don't support COD
            $table->boolean('requireVerification')->default(false); // Require phone verification
            $table->integer('maxOrdersPerDay')->nullable(); // Max COD orders per customer per day
            $table->text('terms')->nullable(); // COD terms and conditions
            $table->text('instructions')->nullable(); // Instructions for customers
            $table->json('metadata')->nullable(); // Additional COD settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('cod_settings');
    }
};
