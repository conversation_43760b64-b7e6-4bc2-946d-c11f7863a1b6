<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('referral_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->decimal('referralAmount', 10, 2)->default(0); // Referral reward amount
            $table->string('rewardType')->default('fixed'); // fixed, percentage
            $table->decimal('minOrderAmount', 10, 2)->default(0); // Minimum order for referral
            $table->integer('maxReferrals')->nullable(); // Maximum referrals per user
            $table->integer('validityDays')->default(30); // Referral validity in days
            $table->boolean('isEnabled')->default(true); // Referral enabled/disabled
            $table->json('conditions')->nullable(); // Referral conditions
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('referral_settings');
    }
};
