<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('vendor_filters', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('name'); // Filter name (e.g., "Cuisine Type", "Price Range")
            $table->string('type')->default('checkbox'); // checkbox, radio, range, select
            $table->text('description')->nullable(); // Filter description
            $table->json('options')->nullable(); // Available filter options
            $table->boolean('isActive')->default(true); // Filter status
            $table->boolean('isRequired')->default(false); // Required filter
            $table->integer('sortOrder')->default(0); // Display order
            $table->string('category')->default('general'); // general, cuisine, price, rating, etc.
            $table->json('metadata')->nullable(); // Additional filter metadata
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isActive']);
            $table->index(['category', 'isActive']);
            $table->index(['sortOrder']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('vendor_filters');
    }
};
