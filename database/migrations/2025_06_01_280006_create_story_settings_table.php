<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('story_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(false); // Story feature enabled/disabled
            $table->integer('maxDuration')->default(24); // Story duration in hours
            $table->integer('maxStories')->default(10); // Maximum stories per user
            $table->json('allowedTypes')->nullable(); // Allowed story types (image, video)
            $table->integer('maxFileSize')->default(10); // Max file size in MB
            $table->json('filters')->nullable(); // Story filters
            $table->boolean('autoDelete')->default(true); // Auto delete expired stories
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('story_settings');
    }
};
