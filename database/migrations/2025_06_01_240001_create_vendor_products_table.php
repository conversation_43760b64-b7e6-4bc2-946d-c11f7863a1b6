<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('vendor_products', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('vendorID'); // Restaurant ID
            $table->string('foodID'); // Food item ID
            $table->decimal('vendorPrice', 10, 2)->nullable(); // Vendor specific price
            $table->decimal('vendorDiscountPrice', 10, 2)->nullable(); // Vendor discount price
            $table->boolean('isAvailable')->default(true); // Product availability
            $table->integer('stock')->nullable(); // Available stock
            $table->integer('minOrderQuantity')->default(1); // Minimum order quantity
            $table->integer('maxOrderQuantity')->nullable(); // Maximum order quantity
            $table->text('vendorNotes')->nullable(); // Vendor specific notes
            $table->json('vendorAddOns')->nullable(); // Vendor specific add-ons
            $table->json('vendorVariants')->nullable(); // Vendor specific variants
            $table->integer('preparationTime')->nullable(); // Preparation time in minutes
            $table->boolean('isSpecial')->default(false); // Special/featured item
            $table->integer('sortOrder')->default(0); // Display order
            $table->timestamp('availableFrom')->nullable(); // Available from time
            $table->timestamp('availableUntil')->nullable(); // Available until time
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['vendorID']);
            $table->index(['foodID']);
            $table->index(['vendorID', 'isAvailable']);
            $table->index(['vendorID', 'isSpecial']);
            $table->unique(['vendorID', 'foodID']); // One product per vendor
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('vendor_products');
    }
};
