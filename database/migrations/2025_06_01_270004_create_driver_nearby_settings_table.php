<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('driver_nearby_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('zoneID')->nullable(); // Specific zone (null for global)
            $table->decimal('searchRadius', 8, 2)->default(5); // Search radius in km
            $table->integer('maxDriversToShow')->default(10); // Maximum drivers to show
            $table->integer('assignmentTimeout')->default(60); // Assignment timeout in seconds
            $table->integer('maxAssignmentAttempts')->default(3); // Max assignment attempts
            $table->string('assignmentMethod')->default('nearest'); // nearest, rating, random
            $table->boolean('considerRating')->default(true); // Consider driver rating
            $table->decimal('minDriverRating', 3, 2)->default(3.0); // Minimum driver rating
            $table->boolean('considerCompletedOrders')->default(true); // Consider completed orders
            $table->integer('minCompletedOrders')->default(5); // Minimum completed orders
            $table->boolean('allowBusyDrivers')->default(false); // Allow busy drivers
            $table->integer('locationUpdateInterval')->default(30); // Location update interval in seconds
            $table->integer('offlineThreshold')->default(300); // Offline threshold in seconds
            $table->json('priorityZones')->nullable(); // Priority zones for driver assignment
            $table->json('blacklistedDrivers')->nullable(); // Blacklisted driver IDs
            $table->boolean('enableAutoAssignment')->default(true); // Enable auto assignment
            $table->boolean('enableManualAssignment')->default(true); // Enable manual assignment
            $table->json('workingHours')->nullable(); // Working hours for driver assignment
            $table->json('metadata')->nullable(); // Additional driver nearby settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['zoneID']);
            $table->index(['enableAutoAssignment']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('driver_nearby_settings');
    }
};
