<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('push_notification_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(true); // Push notifications enabled/disabled
            $table->string('firebaseCloudKey')->nullable(); // Firebase Cloud Messaging key
            $table->string('apiKey')->nullable(); // Firebase API key
            $table->string('databaseURL')->nullable(); // Firebase database URL
            $table->string('storageBucket')->nullable(); // Firebase storage bucket
            $table->string('appId')->nullable(); // Firebase app ID
            $table->string('authDomain')->nullable(); // Firebase auth domain
            $table->string('projectId')->nullable(); // Firebase project ID
            $table->string('messageSenderId')->nullable(); // Firebase message sender ID
            $table->string('measurementId')->nullable(); // Firebase measurement ID
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('push_notification_settings');
    }
};
