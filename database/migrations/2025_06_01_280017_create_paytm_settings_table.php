<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('paytm_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(false); // Paytm enabled/disabled
            $table->boolean('isSandboxEnabled')->default(true); // Sandbox mode
            $table->string('merchantId')->nullable(); // Paytm merchant ID
            $table->string('merchantKey')->nullable(); // Paytm merchant key
            $table->string('website')->nullable(); // Paytm website
            $table->string('industryType')->nullable(); // Industry type
            $table->string('channelId')->nullable(); // Channel ID
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->decimal('processingFee', 5, 2)->default(2.0); // Processing fee percentage
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('paytm_settings');
    }
};
