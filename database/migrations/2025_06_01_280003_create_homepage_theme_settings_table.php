<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('homepage_theme_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('theme')->default('theme_1'); // Selected theme
            $table->json('colors')->nullable(); // Theme colors
            $table->json('layout')->nullable(); // Layout settings
            $table->json('banners')->nullable(); // Banner settings
            $table->json('sections')->nullable(); // Homepage sections
            $table->boolean('isEnabled')->default(true); // Theme enabled/disabled
            $table->json('customCSS')->nullable(); // Custom CSS
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('homepage_theme_settings');
    }
};
