<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('restaurant_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('autoApproval')->default(false); // Auto approve restaurants
            $table->decimal('commissionRate', 5, 2)->default(10); // Default commission rate
            $table->integer('minOrderAmount')->default(0); // Minimum order amount
            $table->integer('maxDeliveryTime')->default(60); // Max delivery time in minutes
            $table->json('workingHours')->nullable(); // Default working hours
            $table->json('categories')->nullable(); // Available categories
            $table->boolean('requireVerification')->default(true); // Require document verification
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['autoApproval']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('restaurant_settings');
    }
};
