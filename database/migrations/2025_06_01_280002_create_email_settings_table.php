<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('email_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('smtpHost')->nullable(); // SMTP host
            $table->integer('smtpPort')->default(587); // SMTP port
            $table->string('smtpUsername')->nullable(); // SMTP username
            $table->string('smtpPassword')->nullable(); // SMTP password
            $table->string('encryption')->default('tls'); // Encryption type
            $table->string('fromEmail')->nullable(); // From email address
            $table->string('fromName')->nullable(); // From name
            $table->boolean('isEnabled')->default(true); // Email enabled/disabled
            $table->json('templates')->nullable(); // Email templates
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('email_settings');
    }
};
