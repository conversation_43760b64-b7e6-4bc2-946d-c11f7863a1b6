<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('payment_gateway_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('gatewayName'); // stripe, paypal, razorpay, mercadopago, etc.
            $table->string('displayName'); // Display name for users
            $table->boolean('isEnabled')->default(false); // Gateway enabled/disabled
            $table->boolean('isTestMode')->default(true); // Test/Live mode
            $table->text('publicKey')->nullable(); // Public/Publishable key
            $table->text('secretKey')->nullable(); // Secret/Private key (encrypted)
            $table->text('webhookSecret')->nullable(); // Webhook secret
            $table->string('currency')->default('USD'); // Default currency
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->json('supportedCountries')->nullable(); // Supported countries
            $table->decimal('transactionFee', 5, 2)->default(0); // Transaction fee percentage
            $table->decimal('fixedFee', 10, 2)->default(0); // Fixed fee per transaction
            $table->decimal('minAmount', 10, 2)->default(1); // Minimum transaction amount
            $table->decimal('maxAmount', 10, 2)->nullable(); // Maximum transaction amount
            $table->json('configuration')->nullable(); // Gateway specific configuration
            $table->string('webhookUrl')->nullable(); // Webhook URL
            $table->string('successUrl')->nullable(); // Success redirect URL
            $table->string('cancelUrl')->nullable(); // Cancel redirect URL
            $table->string('logoUrl')->nullable(); // Gateway logo URL
            $table->text('description')->nullable(); // Gateway description
            $table->integer('sortOrder')->default(0); // Display order
            $table->json('metadata')->nullable(); // Additional gateway settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['gatewayName']);
            $table->index(['isEnabled']);
            $table->index(['sortOrder']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('payment_gateway_settings');
    }
};
