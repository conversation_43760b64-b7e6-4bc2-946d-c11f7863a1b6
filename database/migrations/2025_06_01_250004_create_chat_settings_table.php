<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('chat_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('userID'); // User ID
            $table->string('userType'); // customer, vendor, driver, admin
            $table->boolean('enableNotifications')->default(true); // Enable chat notifications
            $table->boolean('enableSounds')->default(true); // Enable notification sounds
            $table->boolean('enableVibration')->default(true); // Enable vibration
            $table->string('notificationTime')->default('always'); // always, business_hours, never
            $table->time('businessHoursStart')->nullable(); // Business hours start
            $table->time('businessHoursEnd')->nullable(); // Business hours end
            $table->boolean('autoReply')->default(false); // Enable auto reply
            $table->text('autoReplyMessage')->nullable(); // Auto reply message
            $table->boolean('showOnlineStatus')->default(true); // Show online status
            $table->boolean('showReadReceipts')->default(true); // Show read receipts
            $table->boolean('allowFileSharing')->default(true); // Allow file sharing
            $table->integer('maxFileSize')->default(10); // Max file size in MB
            $table->json('allowedFileTypes')->nullable(); // Allowed file types
            $table->string('language')->default('en'); // Chat language
            $table->string('theme')->default('light'); // light, dark, auto
            $table->json('blockedUsers')->nullable(); // Blocked user IDs
            $table->json('preferences')->nullable(); // Additional user preferences
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['userID', 'userType']);
            $table->unique(['userID', 'userType']); // One setting per user
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('chat_settings');
    }
};
