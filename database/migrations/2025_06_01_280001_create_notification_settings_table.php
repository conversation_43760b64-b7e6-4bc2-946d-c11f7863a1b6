<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('notification_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('senderId')->nullable(); // Firebase sender ID
            $table->text('serviceJson')->nullable(); // Firebase service JSON
            $table->boolean('isEnabled')->default(true); // Notifications enabled/disabled
            $table->json('channels')->nullable(); // Notification channels (email, push, sms)
            $table->json('templates')->nullable(); // Notification templates
            $table->json('schedules')->nullable(); // Notification schedules
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('notification_settings');
    }
};
