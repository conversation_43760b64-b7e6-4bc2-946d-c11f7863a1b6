<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('vendor_categories', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('name'); // Category name (e.g., "Fast Food", "Fine Dining")
            $table->text('description')->nullable(); // Category description
            $table->string('photo')->nullable(); // Category image
            $table->string('icon')->nullable(); // Category icon
            $table->string('color')->nullable(); // Category color theme
            $table->boolean('isActive')->default(true); // Category status
            $table->integer('sortOrder')->default(0); // Display order
            $table->string('parentID')->nullable(); // Parent category for subcategories
            $table->json('metadata')->nullable(); // Additional category data
            $table->decimal('commissionRate', 5, 2)->nullable(); // Commission rate for this category
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isActive']);
            $table->index(['parentID']);
            $table->index(['sortOrder']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('vendor_categories');
    }
};
