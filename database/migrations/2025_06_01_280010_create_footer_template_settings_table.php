<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('footer_template_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->text('template')->nullable(); // Footer template content
            $table->json('links')->nullable(); // Footer links
            $table->json('socialMedia')->nullable(); // Social media links
            $table->string('copyright')->nullable(); // Copyright text
            $table->boolean('isEnabled')->default(true); // Footer enabled/disabled
            $table->json('customCSS')->nullable(); // Custom CSS
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('footer_template_settings');
    }
};
