<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('referrals', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('referrerID'); // User who made the referral
            $table->string('referrerType')->default('customer'); // customer, vendor, driver
            $table->string('referredID'); // User who was referred
            $table->string('referredType')->default('customer'); // customer, vendor, driver
            $table->string('referralCode'); // Unique referral code
            $table->string('status')->default('pending'); // pending, completed, expired, cancelled
            $table->decimal('referrerReward', 10, 2)->default(0); // Reward for referrer
            $table->decimal('referredReward', 10, 2)->default(0); // Reward for referred user
            $table->string('rewardType')->default('wallet'); // wallet, discount, points
            $table->timestamp('completedAt')->nullable(); // When referral was completed
            $table->timestamp('expiresAt')->nullable(); // Referral expiry date
            $table->string('campaignID')->nullable(); // Related campaign if any
            $table->json('metadata')->nullable(); // Additional referral data
            $table->text('notes')->nullable(); // Admin notes
            $table->boolean('isActive')->default(true); // Referral status
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['referrerID', 'referrerType']);
            $table->index(['referredID', 'referredType']);
            $table->index(['referralCode']);
            $table->index(['status']);
            $table->index(['campaignID']);
            $table->index(['expiresAt']);
            $table->unique(['referralCode']); // Unique referral codes
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('referrals');
    }
};
