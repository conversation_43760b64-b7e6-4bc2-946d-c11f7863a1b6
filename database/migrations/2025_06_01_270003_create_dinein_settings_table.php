<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('dinein_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('vendorID')->nullable(); // Specific vendor (null for global)
            $table->boolean('isEnabled')->default(true); // Dine-in enabled/disabled
            $table->boolean('requireReservation')->default(false); // Require table reservation
            $table->integer('maxAdvanceBookingDays')->default(30); // Max days for advance booking
            $table->integer('minAdvanceBookingHours')->default(1); // Min hours for advance booking
            $table->integer('maxPartySize')->default(10); // Maximum party size
            $table->integer('defaultReservationDuration')->default(120); // Default reservation duration in minutes
            $table->decimal('reservationFee', 10, 2)->default(0); // Reservation fee
            $table->boolean('allowWalkIns')->default(true); // Allow walk-in customers
            $table->json('availableTimeSlots')->nullable(); // Available time slots for reservation
            $table->json('blockedTimeSlots')->nullable(); // Blocked time slots
            $table->json('specialDaySettings')->nullable(); // Special settings for holidays/events
            $table->boolean('requireDeposit')->default(false); // Require deposit for reservation
            $table->decimal('depositAmount', 10, 2)->default(0); // Deposit amount
            $table->string('depositType')->default('fixed'); // fixed, percentage
            $table->text('cancellationPolicy')->nullable(); // Cancellation policy
            $table->integer('cancellationHours')->default(24); // Hours before cancellation allowed
            $table->text('terms')->nullable(); // Dine-in terms and conditions
            $table->json('metadata')->nullable(); // Additional dine-in settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['vendorID']);
            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('dinein_settings');
    }
};
