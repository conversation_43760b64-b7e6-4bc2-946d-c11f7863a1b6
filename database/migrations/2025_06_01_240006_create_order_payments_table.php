<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('order_payments', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('orderID'); // Order ID
            $table->string('paymentMethod'); // cash, card, wallet, online
            $table->decimal('amount', 10, 2); // Payment amount
            $table->string('status'); // pending, completed, failed, refunded
            $table->string('transactionID')->nullable(); // External transaction ID
            $table->string('gatewayResponse')->nullable(); // Payment gateway response
            $table->json('metadata')->nullable(); // Additional payment data
            $table->timestamp('paidAt')->nullable(); // Payment completion time
            $table->timestamp('refundedAt')->nullable(); // Refund time
            $table->decimal('refundAmount', 10, 2)->nullable(); // Refund amount
            $table->text('notes')->nullable(); // Payment notes
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['orderID']);
            $table->index(['status']);
            $table->index(['paymentMethod']);
            $table->index(['transactionID']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('order_payments');
    }
};
