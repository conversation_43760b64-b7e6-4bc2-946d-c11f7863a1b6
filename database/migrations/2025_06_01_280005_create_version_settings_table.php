<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('version_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('appVersion')->nullable(); // Current app version
            $table->string('minVersion')->nullable(); // Minimum required version
            $table->string('platform')->nullable(); // android, ios, web
            $table->boolean('forceUpdate')->default(false); // Force update required
            $table->text('updateMessage')->nullable(); // Update message
            $table->string('downloadUrl')->nullable(); // Download URL
            $table->json('features')->nullable(); // Version features
            $table->boolean('isEnabled')->default(true); // Version check enabled/disabled
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['platform', 'isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('version_settings');
    }
};
