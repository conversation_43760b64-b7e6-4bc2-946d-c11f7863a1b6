<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('stripe_settings', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->boolean('isEnabled')->default(false); // Stripe enabled/disabled
            $table->boolean('isLiveMode')->default(false); // Live mode
            $table->string('publishableKey')->nullable(); // Stripe publishable key
            $table->string('secretKey')->nullable(); // Stripe secret key
            $table->string('webhookSecret')->nullable(); // Webhook secret
            $table->string('webhookUrl')->nullable(); // Webhook URL
            $table->json('supportedCurrencies')->nullable(); // Supported currencies
            $table->decimal('processingFee', 5, 2)->default(2.9); // Processing fee percentage
            $table->decimal('fixedFee', 10, 2)->default(0.30); // Fixed fee amount
            $table->json('metadata')->nullable(); // Additional settings
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['isEnabled']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('stripe_settings');
    }
};
