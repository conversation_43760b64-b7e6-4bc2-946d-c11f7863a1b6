<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('message_read_status', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('messageID'); // Message ID
            $table->string('conversationID'); // Conversation ID
            $table->string('userID'); // User who read the message
            $table->string('userType'); // customer, vendor, driver, admin
            $table->string('status')->default('delivered'); // sent, delivered, read, failed
            $table->timestamp('deliveredAt')->nullable(); // When message was delivered
            $table->timestamp('readAt')->nullable(); // When message was read
            $table->string('deviceID')->nullable(); // Device that read the message
            $table->string('platform')->nullable(); // ios, android, web
            $table->json('metadata')->nullable(); // Additional read status data
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['messageID']);
            $table->index(['conversationID']);
            $table->index(['userID', 'userType']);
            $table->index(['messageID', 'userID']);
            $table->index(['status']);
            $table->index(['readAt']);
            $table->unique(['messageID', 'userID', 'userType']); // One read status per user per message
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('message_read_status');
    }
};
