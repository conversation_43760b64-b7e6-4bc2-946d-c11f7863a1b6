<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('firebase')->create('email_templates', function (Blueprint $table) {
            $table->string('id')->primary(); // Firebase style ID
            $table->string('name'); // Template name
            $table->string('slug')->unique(); // Template slug
            $table->string('subject'); // Email subject
            $table->text('body'); // Email body (HTML)
            $table->text('bodyText')->nullable(); // Plain text version
            $table->string('type'); // welcome, order_confirmation, password_reset, etc.
            $table->string('language')->default('en'); // Template language
            $table->json('variables')->nullable(); // Available template variables
            $table->boolean('isActive')->default(true); // Template status
            $table->boolean('isDefault')->default(false); // Default template for type
            $table->string('fromName')->nullable(); // Sender name
            $table->string('fromEmail')->nullable(); // Sender email
            $table->string('replyTo')->nullable(); // Reply-to email
            $table->json('metadata')->nullable(); // Additional template data
            $table->timestamp('createdAt')->nullable();
            $table->timestamps();

            $table->index(['type']);
            $table->index(['language']);
            $table->index(['isActive']);
            $table->index(['isDefault']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('firebase')->dropIfExists('email_templates');
    }
};
