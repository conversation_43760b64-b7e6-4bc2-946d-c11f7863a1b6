<?php

require_once 'vendor/autoload.php';

// Load Laravel configuration
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== إنشاء الجداول المفقودة في PostgreSQL ===\n";

try {
    // Test connection
    $connection = DB::connection('firebase');
    $connection->select('SELECT 1');
    echo "✓ اتصال قاعدة البيانات يعمل بشكل صحيح\n\n";

    // List of tables to create
    $tablesToCreate = [
        'cod_settings' => [
            'id' => 'VARCHAR(255) PRIMARY KEY',
            'isEnabled' => 'BOOLEAN DEFAULT TRUE',
            'minOrderAmount' => 'DECIMAL(10,2) DEFAULT 0',
            'maxOrderAmount' => 'DECIMAL(10,2)',
            'extraCharge' => 'DECIMAL(10,2) DEFAULT 0',
            'chargeType' => 'VARCHAR(50) DEFAULT \'fixed\'',
            'allowedZones' => 'JSON',
            'excludedZones' => 'JSON',
            'allowedCategories' => 'JSON',
            'excludedCategories' => 'JSON',
            'requireVerification' => 'BOOLEAN DEFAULT FALSE',
            'maxOrdersPerDay' => 'INTEGER',
            'terms' => 'TEXT',
            'instructions' => 'TEXT',
            'metadata' => 'JSON',
            'createdAt' => 'TIMESTAMP',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ],
        'contact_settings' => [
            'id' => 'VARCHAR(255) PRIMARY KEY',
            'companyName' => 'VARCHAR(255)',
            'address' => 'TEXT',
            'phone' => 'VARCHAR(50)',
            'alternatePhone' => 'VARCHAR(50)',
            'email' => 'VARCHAR(255)',
            'supportEmail' => 'VARCHAR(255)',
            'website' => 'VARCHAR(255)',
            'facebookUrl' => 'VARCHAR(255)',
            'twitterUrl' => 'VARCHAR(255)',
            'instagramUrl' => 'VARCHAR(255)',
            'linkedinUrl' => 'VARCHAR(255)',
            'youtubeUrl' => 'VARCHAR(255)',
            'latitude' => 'DECIMAL(10,8)',
            'longitude' => 'DECIMAL(11,8)',
            'businessHours' => 'JSON',
            'supportHours' => 'JSON',
            'timezone' => 'VARCHAR(50) DEFAULT \'UTC\'',
            'aboutUs' => 'TEXT',
            'privacyPolicy' => 'TEXT',
            'termsOfService' => 'TEXT',
            'metadata' => 'JSON',
            'createdAt' => 'TIMESTAMP',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ],
        'referrals' => [
            'id' => 'VARCHAR(255) PRIMARY KEY',
            'referrerID' => 'VARCHAR(255)',
            'referrerType' => 'VARCHAR(50) DEFAULT \'customer\'',
            'referredID' => 'VARCHAR(255)',
            'referredType' => 'VARCHAR(50) DEFAULT \'customer\'',
            'referralCode' => 'VARCHAR(100)',
            'status' => 'VARCHAR(50) DEFAULT \'pending\'',
            'referrerReward' => 'DECIMAL(10,2) DEFAULT 0',
            'referredReward' => 'DECIMAL(10,2) DEFAULT 0',
            'rewardType' => 'VARCHAR(50) DEFAULT \'wallet\'',
            'completedAt' => 'TIMESTAMP',
            'expiresAt' => 'TIMESTAMP',
            'campaignID' => 'VARCHAR(255)',
            'metadata' => 'JSON',
            'notes' => 'TEXT',
            'isActive' => 'BOOLEAN DEFAULT TRUE',
            'createdAt' => 'TIMESTAMP',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ],
        'languages' => [
            'id' => 'VARCHAR(255) PRIMARY KEY',
            'code' => 'VARCHAR(10) UNIQUE',
            'name' => 'VARCHAR(100)',
            'nativeName' => 'VARCHAR(100)',
            'direction' => 'VARCHAR(10) DEFAULT \'ltr\'',
            'flag' => 'VARCHAR(10)',
            'isActive' => 'BOOLEAN DEFAULT TRUE',
            'isDefault' => 'BOOLEAN DEFAULT FALSE',
            'sortOrder' => 'INTEGER DEFAULT 0',
            'completionPercentage' => 'DECIMAL(5,2) DEFAULT 0',
            'regions' => 'JSON',
            'metadata' => 'JSON',
            'createdAt' => 'TIMESTAMP',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ],
        'admin_commissions' => [
            'id' => 'VARCHAR(255) PRIMARY KEY',
            'vendorID' => 'VARCHAR(255)',
            'categoryID' => 'VARCHAR(255)',
            'zoneID' => 'VARCHAR(255)',
            'commissionType' => 'VARCHAR(50)',
            'commissionValue' => 'DECIMAL(10,2)',
            'fixedAmount' => 'DECIMAL(10,2)',
            'minCommission' => 'DECIMAL(10,2)',
            'maxCommission' => 'DECIMAL(10,2)',
            'applicableOn' => 'VARCHAR(50) DEFAULT \'order_total\'',
            'isEnabled' => 'BOOLEAN DEFAULT TRUE',
            'effectiveFrom' => 'DATE',
            'effectiveUntil' => 'DATE',
            'conditions' => 'JSON',
            'description' => 'TEXT',
            'priority' => 'INTEGER DEFAULT 0',
            'createdAt' => 'TIMESTAMP',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ]
    ];

    // Check existing tables
    $existingTables = $connection->select("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE'");
    $existingTableNames = array_column($existingTables, 'table_name');

    echo "الجداول الموجودة حالياً: " . count($existingTableNames) . "\n";

    // Create missing tables
    foreach ($tablesToCreate as $tableName => $columns) {
        if (in_array($tableName, $existingTableNames)) {
            echo "✓ الجدول $tableName موجود بالفعل\n";
            continue;
        }

        echo "إنشاء جدول $tableName...\n";
        
        $sql = "CREATE TABLE $tableName (\n";
        $columnDefinitions = [];
        foreach ($columns as $columnName => $columnType) {
            $columnDefinitions[] = "  $columnName $columnType";
        }
        $sql .= implode(",\n", $columnDefinitions);
        $sql .= "\n)";

        try {
            $connection->statement($sql);
            echo "✓ تم إنشاء جدول $tableName بنجاح\n";
        } catch (Exception $e) {
            echo "✗ خطأ في إنشاء جدول $tableName: " . $e->getMessage() . "\n";
        }
    }

    // Final check
    echo "\n=== التحقق النهائي ===\n";
    $finalTables = $connection->select("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE' ORDER BY table_name");
    echo "إجمالي الجداول بعد الإنشاء: " . count($finalTables) . "\n";

    foreach ($tablesToCreate as $tableName => $columns) {
        $exists = $connection->select("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$tableName'");
        if ($exists) {
            echo "✓ $tableName\n";
        } else {
            echo "✗ $tableName (مفقود)\n";
        }
    }

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
}

echo "\nتم الانتهاء من العملية.\n";
?>
